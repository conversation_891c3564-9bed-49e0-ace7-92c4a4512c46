import React, { useContext, useRef, useState, useEffect } from "react";
import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import postimg from "../../../assets/images/postimg1.svg";
import { InputAdornment, Dialog } from "@mui/material";
import { Formik, Form, Field } from "formik";
import { FormGroup, FormControlLabel, Stack, Typography } from "@mui/material";
import Android12Switch from "./Android12Switch";
import moment from "moment";
import { CustomTextField } from "../custom/CustomTextField";
import KeyboardArrowRightIcon from "@mui/icons-material/KeyboardArrowRight";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";

import { URL as API_URL } from "../../../helpers/constant/Url";
import { setApiMessage } from "../../../helpers/context/toaster";
import apiInstance from "../../../helpers/Axios/axiosINstance";
import { fetchFromStorage } from "../../../helpers/context/storage";
import siteConstant from "../../../helpers/constant/siteConstant";
import { IntlContext } from "../../../App";
import { getFileType } from "../../../helpers/constant/utils";
import { ListGroup, ListGroupItem } from "flowbite-react";
import Spinner from "../../../helpers/UI/Spinner";
import ProfilePage from "../profile";
import { useDispatch, useSelector } from "react-redux";
import { fetchProfile, selectedUser } from "../../../redux/slices/profileFetch";
import { FiPlus } from "react-icons/fi";
import { IoMdClose } from "react-icons/io";
import HashtagTextField from "./HashtagTextField.jsx";
import TagInTextField from "./TagInTextField.jsx";
import { scheduleData } from "../../../redux/slices/schedulePost";
import { FFmpeg } from "@ffmpeg/ffmpeg";
import { fetchFile } from "@ffmpeg/util";
import locationIcon from "../../../assets/images/Upload_Post/location.svg";
import scheduleIcon from "../../../assets/images/Upload_Post/schedule.svg";
import tagIcon from "../../../assets/images/Upload_Post/tag.svg";
import draft from "../../../assets/images/Upload_Post/draft.svg";
import { useBrand } from "../../../helpers/context/BrandContext.jsx";

// Add this utility function at the top of the file, after imports
const getSecureUrl = (url) => {
  if (!url) return "";
  // If it's a blob URL or data URL, return as is
  if (url.startsWith("blob:") || url.startsWith("data:")) return url;
  // If it's an HTTP URL, convert to HTTPS
  if (url.startsWith("http:")) return url.replace("http:", "https:");
  return url;
};

// Utility function to detect MOV files
const isMovFile = (file) => {
  if (!file) return false;
  if (file.name) {
    return file.name.toLowerCase().endsWith(".mov");
  }
  if (file.type) {
    return file.type === "video/quicktime" || file.type === "video/x-quicktime";
  }
  return false;
};

// Utility function to get file extension
const getFileExtension = (filename) => {
  if (!filename) return "";
  return filename.split(".").pop().toLowerCase();
};

// Utility function to convert data URL to File object
const dataURLtoFile = (dataurl, filename) => {
  const arr = dataurl.split(",");
  const mime = arr[0].match(/:(.*?);/)[1];
  const bstr = atob(arr[1]);
  let n = bstr.length;
  const u8arr = new Uint8Array(n);
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  return new File([u8arr], filename, { type: mime });
};

// Utility function to validate file types with detailed error messages
const validateFileType = (file) => {
  const validImageTypes = [
    "image/jpeg",
    "image/jpg",
    "image/png",
    // "image/gif", // Remove GIF from valid types
    "image/webp",
    "image/bmp",
    "image/tiff",
    "image/svg+xml",
  ];
  const validVideoTypes = [
    "video/mp4",
    "video/avi",
    "video/mov",
    "video/wmv",
    "video/flv",
    "video/webm",
    "video/mkv",
    "video/quicktime",
    "video/x-quicktime",
  ];

  // Reject GIF files
  if (file.type === "image/gif") {
    return {
      isValid: false,
      error: `GIF files are not allowed. Please select another image or video file.`,
    };
  }

  const isValidImage =
    validImageTypes.includes(file.type) || file.type.startsWith("image/");
  const isValidVideo =
    validVideoTypes.includes(file.type) || file.type.startsWith("video/");

  if (!isValidImage && !isValidVideo) {
    return {
      isValid: false,
      error: `Unsupported file type: ${file.type}. Please upload images (JPEG, PNG, WebP, etc.) or videos (MP4, AVI, MOV, etc.)`,
    };
  }

  return { isValid: true, error: null };
};

// Utility function to verify file integrity before upload
const verifyFileIntegrity = async (file) => {
  return new Promise((resolve) => {
    if (!file || !(file instanceof File)) {
      resolve({ isValid: false, error: "Invalid file object" });
      return;
    }

    if (file.size === 0) {
      resolve({ isValid: false, error: "File is empty" });
      return;
    }

    // For GIF files, try to read a small portion to verify it's readable
    if (file.type === "image/gif") {
      const reader = new FileReader();
      reader.onload = () => {
        resolve({ isValid: true, error: null });
      };
      reader.onerror = () => {
        resolve({ isValid: false, error: "File is corrupted or unreadable" });
      };
      // Read just the first 1KB to verify file integrity
      reader.readAsArrayBuffer(file.slice(0, 1024));
    } else {
      resolve({ isValid: true, error: null });
    }
  });
};

// Utility function to create a clean File object for GIF files
const createCleanGifFile = async (originalFile) => {
  return new Promise((resolve, reject) => {
    if (originalFile.type !== "image/gif") {
      resolve(originalFile);
      return;
    }

    console.log("Creating clean GIF file from:", originalFile.name);

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const arrayBuffer = e.target.result;
        const blob = new Blob([arrayBuffer], { type: "image/gif" });
        const cleanFile = new File([blob], originalFile.name, {
          type: "image/gif",
          lastModified: originalFile.lastModified || Date.now(),
        });

        console.log("Created clean GIF file:", {
          name: cleanFile.name,
          type: cleanFile.type,
          size: cleanFile.size,
          originalSize: originalFile.size,
        });

        resolve(cleanFile);
      } catch (error) {
        console.error("Error creating clean GIF file:", error);
        reject(error);
      }
    };

    reader.onerror = (error) => {
      console.error("Error reading original GIF file:", error);
      reject(error);
    };

    reader.readAsArrayBuffer(originalFile);
  });
};

// function highlightHashtagsAndMentions(text) {
//   const regex = /([#@][\w-]+)/g;
//   const parts = text.split(regex);

//   return (
//     parts?.map((part, index) => {
//       if (part.match(regex)) {
//         return (<span key={index} style={{ color: part.startsWith('#') || part.startsWith('@') ? 'blue' : 'green' }}>
//           {part}
//         </span>)
//       }
//       return part;
//     })
//   )
// }

const VideoPlayer = ({ videoData, localesData }) => {
  const videoRef = useRef(null);
  useEffect(() => {
    if (videoRef.current && videoData.startTime !== undefined) {
      // Set the start time for the video chunk
      videoRef.current.currentTime = videoData.startTime;
      // Add listener to stop video at end time
      const handleTimeUpdate = () => {
        if (videoRef.current.currentTime >= videoData.endTime) {
          videoRef.current.pause();
          videoRef.current.currentTime = videoData.startTime;
        }
      };
      videoRef.current.addEventListener("timeupdate", handleTimeUpdate);
      return () => {
        if (videoRef.current) {
          videoRef.current.removeEventListener("timeupdate", handleTimeUpdate);
        }
      };
    }
  }, [videoData]);

  return (
    <div className="relative">
      <video
        ref={videoRef}
        src={videoData.url}
        controls
        className="md:w-[340px] w-[250px] md:h-[360px] h-[250px] max-h-[440px] inline-block rounded-[20px] object-contain mx-auto"
      >
        {localesData?.USER_WEB?.YOUR_BROWSER_DOES_NOT_SUPPORT_THE_VIDEO_TAG}
      </video>
      {videoData.totalChunks > 1 && (
        <div className="absolute top-2 right-2 bg-[#EDEBEC] bg-opacity-70 text-Red px-2 py-1 rounded-full text-xs">
          {videoData.chunkIndex + 1}/{videoData.totalChunks}
        </div>
      )}
    </div>
  );
};

const UploadPost = ({
  open,
  onClose,
  renderHighlightedText,
  text,
  renderTextFunction,
  suggestionData,
}) => {
  // Debug: Log when component mounts
  React.useEffect(() => {
    console.log("UploadPost component mounted with enhanced GIF support");
  }, []);
  const fileInputRef = useRef(null);
  const [uploaded, setUploaded] = useState(false);
  const [images, setImages] = useState([]);
  const [uploadedFile, setuploadedFile] = useState([]);
  const [uploadedChunkFile, setuploadedChunkFile] = useState([]);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [selected, setSelected] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [shareChecked, setshareChecked] = useState(false);
  const [settings, setSettings] = useState({
    infinite: true,
    speed: 500,
    slidesToShow: 1,
    slidesToScroll: 1,
  });
  const socialMediaConnected = fetchFromStorage(
    siteConstant?.INDENTIFIERS?.USERDATA
  );
  const [startDate, setStartDate] = useState(null);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const intlContext = useContext(IntlContext);
  const localesData = intlContext?.messages;
  const [loading, setLoading] = useState(false);
  const [loadingDraft, setLoadingDraft] = useState(false);
  const [showAll, setShowAll] = useState(false);
  const [search, setSearch] = useState("");
  const [compareData, setCompareDate] = useState([]);
  const [compareDataTag, setCompareDateTag] = useState([]);
  const datePickerRef = useRef(null);
  const dispatch = useDispatch();
  const [currentMediaIndex, setCurrentMediaIndex] = useState(0);
  const topSliderRef = useRef(null);
  const [activeInput, setActiveInput] = useState(null);
  const [isToday, setIsToday] = useState(false);
  let [value, setValue] = useState([]);
  const [selectedIDs, setSelectedIDs] = useState([]);
  const [selectedIDTag, setSelectedIdTag] = useState([]);
  const [dataDis, setDataDis] = useState([]);
  const [dataTag, setDataTag] = useState([]);
  const minTime = new Date();
  const maxTime = new Date().setHours(23, 59);
  const [selectedNames, setSelectedNames] = useState([]);
  const [selectNameHelperOne, setselectNameHelperOne] = useState([]);
  const [selectNameHelperTwo, setselectNameHelperTwo] = useState([]);
  const BrandId = localStorage.getItem("BrandId");
  const subscriptionId = localStorage.getItem("subscriptionId");
  const user = localStorage.getItem("UserId");
  const [searchText, setSearchText] = useState(""); // Initialize with an empty string
  const [locationResults, setLocationResults] = useState([]);
  const [currentLocationAPI, setCurrentLocationAPI] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [isVideoChunkingEnabled, setIsVideoChunkingEnabled] = useState(false);
  const [isChunkingInProgress, setIsChunkingInProgress] = useState(false);
  const [chunkingError, setChunkingError] = useState("");
  const [videoDuration, setVideoDuration] = useState(0);
  const [thumbnails, setThumbnails] = useState({}); // Add this line
  const [isThumbnailLoading, setIsThumbnailLoading] = useState(false);
  const [thumbnailFiles, setThumbnailFiles] = useState([]); // Store thumbnail File objects
  const [showLocationPopup, setShowLocationPopup] = useState(false);
  const [showTagPopup, setShowTagPopup] = useState(false);
  const formActionRef = useRef("post");
  const [thirdPartyConnectedPlatfroms, setThirdPartyConnectedPlatfroms] =
    useState([]);
  const [currentlocation, setCurrentLocation] = useState({
    lat: null,
    lon: null,
  });
  const [isGeolocationLoading, setIsGeolocationLoading] = useState(true);
  const [geolocationError, setGeolocationError] = useState("");
  const [showChunkToggle, setShowChunkToggle] = useState(false);
  const [tempDate, setTempDate] = useState(startDate || new Date());
  const { selectedBrand } = useBrand();
  const [channelsThirdParty, setChannelsThirdParty] = useState([]);

  const platformKeyMap = {
    Instagram: "Instagram",
    Facebook: "Facebook",
    YouTube: "YouTube",
    Pinterest: "Pinterest",
    LinkedIn: "LinkedIn",
    Vimeo: "Vimeo",
    Tumblr: "tumblr",
    Reddit: "reddit",
    Tiktok: "tiktok",
    Threads: "threads",
    x: "x",
    Mastodon: "mastodon",
    Telegram: "telegram"
  };

  const openDatePicker = () => {
    setTempDate(startDate); // Initialize temp date with current value
    setShowDatePicker(true);
  };

  useEffect(() => {
    const shouldShowToggle =
      uploadedFile.length === 1 &&
      uploadedFile[0]?.type.startsWith("video/") &&
      videoDuration > 90;
    setShowChunkToggle(shouldShowToggle);
  }, [uploadedFile, videoDuration]);

  // Add these new state variables
  const [firstMediaType, setFirstMediaType] = useState(null); // 'image' or 'video'
  const [disabledMessage, setDisabledMessage] = useState("");

  console.log("locationResults", locationResults);

  useEffect(() => {
    if (!navigator.geolocation) {
      setError("Geolocation is not supported by your browser");
      setIsGeolocationLoading(false);
      return;
    }

    // Geolocation options
    const options = {
      enableHighAccuracy: true,
      timeout: 10000, // 10 seconds
      maximumAge: 0,
    };

    navigator.geolocation.getCurrentPosition(
      (position) => {
        setCurrentLocation({
          lat: position.coords.latitude,
          lon: position.coords.longitude,
        });
        setIsGeolocationLoading(false);
      },
      (err) => {
        setGeolocationError(`Error: ${err.message}`);
        setIsGeolocationLoading(false);
      },
      options // Add options here
    );
  }, []);

  const getCurrentLocation = async () => {
    try {
      const response = await apiInstance.get(API_URL.SEARCH_LOCATION, {
        params: {
          lat: currentlocation.lat,
          long: currentlocation.lon,
        },
      });
      const locationResults = response.data?.data || [];
      setCurrentLocationAPI(locationResults);
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    getCurrentLocation();
  }, [currentlocation, selectedBrand]);

  useEffect(() => {
    console.log("Current Location from API =========> ", currentLocationAPI);
  }, [currentLocationAPI]);

  const bottomsettings = {
    dots: true,
    infinite: false,
    speed: 500,
    slidesToShow: 4,
    slidesToScroll: 1,
    responsive: [
      {
        breakpoint: 768,
        settings: {
          slidesToShow: 4,
        },
      },
      {
        breakpoint: 480,
        settings: {
          slidesToShow: 4,
        },
      },
    ],
  };

  const [description, setDescription] = useState("");

  const handleDescriptionChange = (event) => {
    setDescription(event.target.value);
  };

  const handleThumbnailClick = (index) => {
    setCurrentMediaIndex(index);
    topSliderRef.current.slickGoTo(index);
  };

  const generateVideoThumbnail = (file, timeInSeconds = 1) => {
    return new Promise((resolve, reject) => {
      const video = document.createElement("video");
      const canvas = document.createElement("canvas");
      const context = canvas.getContext("2d");

      // Set video properties
      video.preload = "metadata";
      video.muted = true;
      video.crossOrigin = "anonymous";

      // Handle video loading
      video.onloadedmetadata = () => {
        // Set canvas dimensions based on video
        canvas.width = video.videoWidth || 320;
        canvas.height = video.videoHeight || 240;

        // Set the time to capture thumbnail
        video.currentTime = Math.min(timeInSeconds, video.duration || 1);
      };

      video.onseeked = () => {
        try {
          context.drawImage(video, 0, 0, canvas.width, canvas.height);
          const thumbnailUrl = canvas.toDataURL("image/jpeg", 0.8);
          URL.revokeObjectURL(video.src);
          resolve(thumbnailUrl);
        } catch (error) {
          console.error("Error generating thumbnail:", error);
          // Fallback to a default thumbnail or video element
          reject(error);
        }
      };

      video.onerror = (error) => {
        console.error("Error loading video:", error);
        URL.revokeObjectURL(video.src);
        reject(error);
      };

      // Create object URL and set as video source
      const objectUrl = URL.createObjectURL(file);
      video.src = objectUrl;
    });
  };

  const addImageHandler = (file) => {
    const filePath = URL.createObjectURL(file);
    const fileType = file.type;
    const isVideo = fileType.startsWith("video/");

    if (isVideo) {
      // Create a temporary video element to get video duration
      const videoElement = document.createElement("video");
      videoElement.src = filePath;

      videoElement.onloadedmetadata = () => {
        const duration = videoElement.duration; // Duration in seconds

        if (duration > 90 && isVideoChunkingEnabled) {
          // If video is longer than 90 seconds and chunking is enabled, divide it into chunks
          const numberOfChunks = Math.ceil(duration / 90);
          const imagesArray = [...images];

          // Add each chunk as a separate entry
          for (let i = 0; i < numberOfChunks; i++) {
            const startTime = i * 90;
            const endTime = Math.min((i + 1) * 90, duration);

            imagesArray.push({
              url: filePath,
              is_video: true,
              startTime: startTime,
              endTime: endTime,
              chunkIndex: i,
              totalChunks: numberOfChunks,
              originalFile: file,
            });
          }

          setImages(imagesArray);

          // Add the original file to uploadedFile
          const filesArray = [...uploadedFile];
          filesArray.push(file);
          setuploadedFile([...filesArray]);
        } else {
          // If video is 90 seconds or less, or chunking is disabled, add it normally
          const imagesArray = [...images];
          imagesArray.push({ url: filePath, is_video: isVideo });
          setImages(imagesArray);

          const filesArray = [...uploadedFile];
          filesArray.push(file);
          setuploadedFile([...filesArray]);
        }
      };
    } else {
      // Handle images normally
      const imagesArray = [...images];
      imagesArray.push({ url: filePath, is_video: isVideo });
      setImages(imagesArray);

      const filesArray = [...uploadedFile];
      filesArray.push(file);
      setuploadedFile([...filesArray]);
    }
  };

  const handleDeleteImage = (index) => {
    if (images.length > 1) {
      // Check if the item is a video chunk
      const deletedItem = images[index];
      const isChunk = deletedItem.hasOwnProperty("chunkIndex");

      // Remove the specific chunk from images
      const updatedImages = images.filter((_, i) => i !== index);

      // If this was the last chunk of a video, remove the file from uploadedFile
      if (isChunk) {
        const sameFileChunks = updatedImages.filter(
          (img) => img.originalFile === deletedItem.originalFile
        );

        if (sameFileChunks.length === 0) {
          // Remove the file from uploadedFile if no chunks remain
          const updatedFiles = uploadedFile.filter(
            (file) => file !== deletedItem.originalFile
          );
          setuploadedFile(updatedFiles);
        }
      } else {
        // For non-chunked files, remove normally
        const temp = [...uploadedFile];
        temp.splice(index, 1);
        setuploadedFile(temp);
      }

      setImages(updatedImages);
    }
  };

  const processVideoChunks = async (file) => {
    try {
      setIsChunkingInProgress(true);
      setChunkingError("");

      const ffmpeg = new FFmpeg();
      await ffmpeg.load();

      const segmentLength = 90; // seconds
      const filePath = URL.createObjectURL(file);
      const fileName = file.name;
      const fileType = file.type;

      const videoElement = document.createElement("video");
      videoElement.src = filePath;

      await new Promise((resolve) => {
        videoElement.onloadedmetadata = resolve;
      });

      const videoDuration = videoElement.duration;
      const numberOfChunks = Math.ceil(videoDuration / segmentLength);

      const newImages = [];
      const chunks = [];
      const newThumbnails = { ...thumbnails };

      // Write the input video file to FFmpeg's in-memory filesystem
      await ffmpeg.writeFile(fileName, await fetchFile(file));

      for (let i = 0; i < numberOfChunks; i++) {
        const startTime = i * segmentLength;
        const endTime = Math.min((i + 1) * segmentLength, videoDuration);
        const outputName = `clip_${i}_${fileName}`;

        // Generate thumbnail at the start of each chunk + 1 second
        const thumbnailTime = startTime + 1;
        const chunkThumbnail = await generateVideoThumbnail(
          file,
          thumbnailTime
        );
        newThumbnails[`chunk_${i}_${fileName}`] = chunkThumbnail;

        newImages.push({
          url: filePath,
          is_video: true,
          startTime,
          endTime,
          chunkIndex: i,
          totalChunks: numberOfChunks,
          originalFile: file,
          thumbnail: chunkThumbnail,
        });

        // Execute FFmpeg command to create the video chunk
        await ffmpeg.exec([
          "-i",
          fileName,
          "-ss",
          `${startTime}`,
          "-t",
          `${segmentLength}`,
          "-c",
          "copy",
          outputName,
        ]);

        // Read the processed chunk from FFmpeg's in-memory filesystem
        const data = await ffmpeg.readFile(outputName);
        const blob = new Blob([data.buffer], { type: fileType });
        const clipFile = new File([blob], outputName, {
          type: fileType,
          lastModified: Date.now(),
        });

        chunks.push(clipFile);
      }

      console.log(chunks);
      // Update state with the new thumbnails, images, and chunks
      setThumbnails(newThumbnails);
      setImages(newImages);
      setuploadedChunkFile(chunks);
    } catch (error) {
      console.error("Error processing video chunks:", error);
      setChunkingError("Failed to process video chunks. Please try again.");
    } finally {
      setIsChunkingInProgress(false);
    }
  };
  const handleFileChange = (event) => {
    const files = event.target.files;
    console.log("Files selected:", files);

    if (files.length > 10) {
      alert("You can only upload maximum 10 files.");
      return;
    }

    if (files.length > 0) {
      // Debug: Log file details
      Array.from(files).forEach((file, index) => {
        console.log(`File ${index + 1}:`, {
          name: file.name,
          type: file.type,
          size: file.size,
          lastModified: file.lastModified,
        });
      });

      // Validate file types using the enhanced validation function
      const validFiles = Array.from(files).filter((file) => {
        const validation = validateFileType(file);

        if (!validation.isValid) {
          console.warn(`Invalid file: ${file.name} - ${validation.error}`);
          setDisabledMessage(validation.error);
          setTimeout(() => setDisabledMessage(""), 5000);
          return false;
        }

        return true;
      });

      if (validFiles.length === 0) {
        console.error("No valid files selected");
        return;
      }

      // Check for mixed types
      if (images.length > 0 && firstMediaType) {
        // Filter files to only include the allowed type
        const filteredFiles = validFiles.filter((file) => {
          const isVideo = file.type.startsWith("video/");
          return (
            (firstMediaType === "video" && isVideo) ||
            (firstMediaType === "image" && !isVideo)
          );
        });
        if (filteredFiles.length === 0) {
          setDisabledMessage(
            `Only ${firstMediaType} files are allowed when the first item is a ${firstMediaType}.`
          );
          setTimeout(() => setDisabledMessage(""), 3000);
          return;
        }
        processFiles(filteredFiles);
      } else {
        // First upload - set the media type
        const firstFile = validFiles[0];
        const isFirstVideo = firstFile.type.startsWith("video/");
        setFirstMediaType(isFirstVideo ? "video" : "image");
        console.log(
          `Setting media type to: ${isFirstVideo ? "video" : "image"}`
        );

        // Filter files to match the first type
        const filteredFiles = validFiles.filter((file) => {
          const isVideo = file.type.startsWith("video/");
          return isFirstVideo ? isVideo : !isVideo;
        });
        if (filteredFiles.length < validFiles.length) {
          setDisabledMessage(
            `Mixed media types not allowed. Only using ${
              isFirstVideo ? "videos" : "images"
            }.`
          );
          setTimeout(() => setDisabledMessage(""), 3000);
        }
        processFiles(filteredFiles);
      }
    }
  };

  const processFiles = (files) => {
    // Reset chunking state
    setIsVideoChunkingEnabled(false);
    setIsChunkingInProgress(false);
    // Clear existing files if needed
    if (images.length === 0) {
      setImages([]);
      setuploadedFile([]);
      setThumbnailFiles([]); // Clear thumbnail files
    }
    setIsThumbnailLoading(true);

    const newThumbnailFiles = [];

    // Process each file
    Array.from(files).forEach((file, fileIndex) => {
      const isVideo = file.type.startsWith("video/");
      if (isVideo) {
        const videoElement = document.createElement("video");
        // Process video and get duration
        videoElement.preload = "metadata";
        videoElement.onloadedmetadata = async () => {
          const duration = videoElement.duration;
          setVideoDuration(duration);
          // Add file to uploadedFile array
          setuploadedFile((prev) => [...prev, file]);

          // Generate thumbnail only for the first video (index 0)
          if (fileIndex === 0) {
            try {
              const thumbnailDataUrl = await generateVideoThumbnail(file, 1);
              const thumbnailFileName = `thumbnail_${file.name.replace(
                /\.[^/.]+$/,
                ""
              )}.jpg`;
              const thumbnailFile = dataURLtoFile(
                thumbnailDataUrl,
                thumbnailFileName
              );
              newThumbnailFiles.push(thumbnailFile);

              // Update thumbnail files state after generating thumbnail for first video
              setThumbnailFiles((prev) => [...prev, thumbnailFile]);
            } catch (error) {
              console.error(
                "Error generating thumbnail for first video:",
                error
              );
            }
          }

          // Create both blob URL and data URL for better compatibility
          const blobUrl = URL.createObjectURL(file);
          const reader = new FileReader();
          reader.onload = (e) => {
            const dataUrl = e.target.result;
            setImages((prev) => [
              ...prev,
              {
                url: dataUrl,
                blobUrl: blobUrl, // Store blob URL as fallback
                is_video: true,
                secureUrl: dataUrl,
                originalFile: file,
              },
            ]);
            setIsThumbnailLoading(false);
          };
          reader.onerror = (error) => {
            console.error("Error reading video file:", error);
            // Fallback to blob URL if data URL fails
            setImages((prev) => [
              ...prev,
              {
                url: blobUrl,
                blobUrl: blobUrl,
                is_video: true,
                secureUrl: blobUrl,
                originalFile: file,
              },
            ]);
            setIsThumbnailLoading(false);
          };
          reader.readAsDataURL(file);
        };
        videoElement.onerror = (error) => {
          console.error("Error loading video metadata:", error);
          setIsThumbnailLoading(false);
        };
        videoElement.src = URL.createObjectURL(file);
      } else {
        // Handle images using FileReader
        console.log(`Processing image file: ${file.name} (${file.type})`);

        // For GIF files, we need to be extra careful to preserve the original file
        if (file.type === "image/gif") {
          console.log(`Special handling for GIF file: ${file.name}`);

          // Create object URL for preview without using FileReader for GIFs
          // as FileReader might corrupt the binary data
          const objectUrl = URL.createObjectURL(file);
          console.log(`Created object URL for GIF: ${objectUrl}`);

          setImages((prev) => [
            ...prev,
            {
              url: objectUrl,
              is_video: false,
              secureUrl: objectUrl,
              originalFile: file, // Keep reference to original file
              isGif: true, // Flag to identify GIF files
            },
          ]);
          setuploadedFile((prev) => [...prev, file]);
          setIsThumbnailLoading(false);
        } else {
          // Use FileReader for non-GIF images
          const reader = new FileReader();
          reader.onload = (e) => {
            console.log(`Successfully loaded image: ${file.name}`);
            setImages((prev) => [
              ...prev,
              {
                url: e.target.result,
                is_video: false,
                secureUrl: e.target.result,
                originalFile: file, // Add original file reference for debugging
              },
            ]);
            setuploadedFile((prev) => [...prev, file]);
            setIsThumbnailLoading(false);
          };
          reader.onerror = (error) => {
            console.error(`Error reading image file ${file.name}:`, error);
            setDisabledMessage(`Failed to load image: ${file.name}`);
            setTimeout(() => setDisabledMessage(""), 3000);
            setIsThumbnailLoading(false);
          };

          reader.readAsDataURL(file);
        }
      }
    });
    setCurrentMediaIndex(0);
    setUploaded(true);
    setSelected(true);
  };

  const handleToggleChunk = async (checked) => {
    setIsVideoChunkingEnabled(checked);
    if (!checked) {
      setuploadedChunkFile([]);
    }
    // If turning on chunking and we have a video longer than 90 seconds
    if (checked && hasOnlyOneVideo() && isVideoLongerThan90Seconds()) {
      // Process the video into chunks
      await processVideoChunks(uploadedFile[0]);
    } else if (!checked && hasOnlyOneVideo()) {
      // If turning off chunking, return to single video
      const videoFile = uploadedFile[0];
      setImages([
        {
          url: URL.createObjectURL(videoFile),
          is_video: true,
        },
      ]);
    }
  };

  const mergeUniqueIDs = (newData) => {
    if (!selectedIDs.includes(newData?.id)) {
      setSelectedIDs((prevSelectedIDs) => {
        const updatedIDs = [...new Set([...prevSelectedIDs, newData?.id])];
        console.log("updatedIDs", updatedIDs);
        return updatedIDs;
      });
      setCompareDate([...compareData, newData]);
    }
  };

  const mergeUniqueIDsForTag = (newData) => {
    if (!selectedIDTag.includes(newData?.id)) {
      setSelectedIdTag((prevSelectedIDs) => {
        const updatedIDs = [...new Set([...prevSelectedIDs, newData?.id])];
        console.log("updatedIDs", updatedIDs);
        return updatedIDs;
      });
      setCompareDateTag([...compareDataTag, newData]);
    }
  };

  const removeText = (data) => {
    const tempForDis = data.map((user) => {
      return user?.id;
    });

    const tempForNameOne = data.map((user) => {
      return user.name;
    });

    setDataDis(() =>
      data.map((user) => {
        return user?.id;
      })
    );

    setselectNameHelperOne(() =>
      data.map((user) => {
        return user.name;
      })
    );

    setSelectedNames([...tempForNameOne, ...selectNameHelperTwo]);
    setSelectedIDs([...tempForDis, ...dataTag]);
    setCompareDate([...data]);
  };

  const removeTextTag = (data) => {
    // setSelectedIDs(...selectedIDs, () =>
    //   data.map((user) => {
    //     return user.id;
    //   })
    // );

    const tempDataTag = data.map((user) => {
      return user?.id;
    });

    const tempForNameTwo = data.map((user) => {
      return user.name;
    });

    setselectNameHelperTwo(() =>
      data.map((user) => {
        return user.name;
      })
    );

    setDataTag(() =>
      data.map((user) => {
        return user?.id;
      })
    );

    console.log(tempDataTag, "wfedfvwnfjwwefc");
    console.log(selectedIDs, "dfwqefo;ugt2e");

    setSelectedNames([...selectNameHelperOne, ...tempForNameTwo]);
    setSelectedIdTag([...tempDataTag]);
    setCompareDateTag([...data]);
    setSelectedIDs([...dataDis, ...tempDataTag]);
  };

  const data = [
    { Delhi: "Delhi" },
    { Mumbai: "Mumbai" },
    { Kolkāta: "Kolkāta" },
    { Bangalore: "Bangalore" },
    { Chennai: "Chennai" },
    { city: "Hyderābād" },
    { Pune: "Pune" },
    { city: "Ahmedabad" },
    { Surat: "sūrat" },
  ];

  // useEffect(() => {
  //   // Ensure startDate is always valid
  //   if (!startDate || !(startDate instanceof Date) || isNaN(startDate)) {
  //     console.warn("Resetting invalid startDate");
  //     setStartDate(new Date());
  //   }
  // }, [startDate]);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        datePickerRef.current &&
        !datePickerRef.current.contains(event.target)
      ) {
        setShowDatePicker(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [datePickerRef]);

  // const filteredLocations = data
  //   .filter((item) => {
  //     const location = Object.values(item)[0].toLowerCase();
  //     return showAll || location.includes(search.toLowerCase());
  //   })
  //   .sort((a, b) => {
  //     const locationA = Object.values(a)[0].toLowerCase();
  //     const locationB = Object.values(b)[0].toLowerCase();
  //     if (locationA.startsWith(search.toLowerCase())) return -1;
  //     if (locationB.startsWith(search.toLowerCase())) return 1;
  //     return 0;
  //   });

  // const handleSelect = (location) => {
  //   setSearch(location);
  //   setShowAll(false);
  // };

  const handleSearchChange = async (event) => {
    const value = event.target.value;
    setSearchText(value); // Update searchText on every keystroke
    if (value.trim().length >= 3) {
      setIsLoading(true);
      try {
        // const response = await apiInstance.get(
        //   `https://staging.flowkar.com/api/search-location/?search_text=${value.trim()}`
        // );
        const response = await apiInstance.get(
          `search-location/?search_text=${value.trim()}`
        );
        console.log("response", response.data.data);

        setLocationResults(response.data.data || []); // Update state with results
      } catch (error) {
        console.error("Error fetching locations:", error);
        setError("Failed to fetch locations");
        setLocationResults([]);
      } finally {
        setIsLoading(false); // End loading state
      }
    } else {
      setLocationResults([]); // Clear results if input length is less than 3
    }
  };

  const handleDateChange = (date) => {
    if (!date) {
      setStartDate(null);
      setIsToday(false);
      return;
    }

    if (!(date instanceof Date) || isNaN(date)) {
      console.error("Invalid date:", date);
      return;
    }

    const normalizedDate = new Date(date);
    normalizedDate.setSeconds(0, 0);
    setStartDate(normalizedDate);
    setIsToday(normalizedDate.toDateString() === new Date().toDateString());
  };

  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
      setUploaded(false);
      clearSelectedIDs();
      handleDialogClose();
    }
  };
  function clearSelectedIDs() {
    setSelectedIDs([]);
  }

  const handleFileInputClick = () => {
    if (!selected) {
      fileInputRef.current.click();
    }
  };

  const getMinTime = () => {
    const date = new Date();
    if (isToday) {
      date.setHours(date.getHours(), date.getMinutes(), 0, 0);
    } else {
      date.setHours(0, 0, 0, 0);
    }
    return date;
  };

  const getMaxTime = () => {
    const date = new Date();
    date.setHours(23, 59, 0, 0);
    return date;
  };

  const handleNextSlide = () => {
    setCurrentImageIndex((prevIndex) => (prevIndex + 1) % images.length);
    console.log("Next Slide:", (currentImageIndex + 1) % images.length);
  };

  const handlePrevSlide = () => {
    setCurrentImageIndex(
      (prevIndex) => (prevIndex - 1 + images.length) % images.length
    );
    console.log(
      "Previous Slide:",
      (currentImageIndex - 1 + images.length) % images.length
    );
  };

  const CustomNextArrow = ({ onClick }) => {
    return (
      <div className="absolute transform -translate-y-1/2 z-10 cursor-pointer bg-[#EFEBE9] p-2 rounded-full sm:top-1/2 sm:right-[30px] md:top-1/2 md:right-[-19px]  top-1/2 right-[10px] ">
        <KeyboardArrowRightIcon
          className="text-[#563D39]"
          onClick={(e) => onClick(e)}
        />
      </div>
    );
  };

  const CustomPrevArrow = ({ onClick }) => {
    return (
      <div className="absolute  transform -translate-y-1/2 z-10 cursor-pointer bg-[#EFEBE9] p-2 rounded-full sm:top-1/2 sm:left-[30px] md:top-1/2 md:left-[-19px] top-1/2 left-[10px]  ">
        <KeyboardArrowRightIcon
          className="rotate-180 text-[#563D39]"
          onClick={(e) => onClick(e)}
        />
      </div>
    );
  };

  useEffect(() => {
    setSettings({
      ...settings,
      nextArrow: <CustomNextArrow onClick={() => handleNextSlide()} />,
      prevArrow: <CustomPrevArrow onClick={() => handlePrevSlide()} />,
      beforeChange: (current, next) => {
        setCurrentImageIndex(next);
      },
    });
  }, []);

  const handleDialogClose = () => {
    // Clean up object URLs to prevent memory leaks
    images.forEach((image) => {
      if (image.url && image.url.startsWith("blob:")) {
        URL.revokeObjectURL(image.url);
      }
      if (image.secureUrl && image.secureUrl.startsWith("blob:")) {
        URL.revokeObjectURL(image.secureUrl);
      }
    });

    onClose();
    setUploaded(false);
    setImages([]);
    setuploadedFile([]);
    setuploadedChunkFile([]);
    setCurrentImageIndex(0);
    setStartDate(null);
    setDescription("");
    setSelectedIDs([]);
    setSelectedIdTag([]);
    setDataDis([]);
    setDataTag([]);
    setSelectedNames([]);
    setselectNameHelperOne([]);
    setselectNameHelperTwo([]);
    setSearchText("");
    setLocationResults([]);
    setIsVideoChunkingEnabled(false);
    setDisabledMessage("");
    setFirstMediaType(null);
    setVideoDuration(0);
    setThumbnails({});
    setThumbnailFiles([]); // Clear thumbnail files
    setIsChunkingInProgress(false);
    setChunkingError("");
    if (fileInputRef.current) fileInputRef.current.value = "";
  };

  const handleDragEnter = (e) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    setIsDragging(false);
  };

  // Modify handleDrop to use the same logic
  const handleDrop = (e) => {
    e.preventDefault();
    setIsDragging(false);

    const files = e.dataTransfer.files;
    console.log("Files dropped:", files);

    // Debug: Log dropped file details
    Array.from(files).forEach((file, index) => {
      console.log(`Dropped file ${index + 1}:`, {
        name: file.name,
        type: file.type,
        size: file.size,
      });
    });

    const acceptedFiles = Array.from(files).filter((file) => {
      const validation = validateFileType(file);

      if (!validation.isValid) {
        console.warn(
          `Rejected dropped file: ${file.name} - ${validation.error}`
        );
        setDisabledMessage(validation.error);
        setTimeout(() => setDisabledMessage(""), 5000);
        return false;
      }

      return true;
    });

    if (acceptedFiles.length > 0) {
      console.log(`Processing ${acceptedFiles.length} valid dropped files`);

      // Check if we already have media and need to enforce type restrictions
      if (images.length > 0 && firstMediaType) {
        // Filter files to only include the allowed type
        const filteredFiles = acceptedFiles.filter((file) => {
          const isVideo = file.type.startsWith("video/");
          return (
            (firstMediaType === "video" && isVideo) ||
            (firstMediaType === "image" && !isVideo)
          );
        });

        if (filteredFiles.length === 0) {
          setDisabledMessage(
            `Only ${firstMediaType} files are allowed when the first item is a ${firstMediaType}.`
          );
          setTimeout(() => setDisabledMessage(""), 3000);
          return;
        }

        // Process only the filtered files
        processFiles(filteredFiles);
      } else {
        // First upload - determine the media type and set restriction
        const firstFile = acceptedFiles[0];
        const isFirstVideo = firstFile.type.startsWith("video/");
        setFirstMediaType(isFirstVideo ? "video" : "image");
        console.log(
          `Setting media type from drop to: ${isFirstVideo ? "video" : "image"}`
        );

        // Filter remaining files to match the first type
        const filteredFiles = acceptedFiles.filter((file) => {
          const isVideo = file.type.startsWith("video/");
          return isFirstVideo ? isVideo : !isVideo;
        });

        if (filteredFiles.length < acceptedFiles.length) {
          setDisabledMessage(
            `Mixed media types not allowed. Only using ${
              isFirstVideo ? "videos" : "images"
            }.`
          );
          setTimeout(() => setDisabledMessage(""), 3000);
        }

        // Process only the filtered files
        processFiles(filteredFiles);
      }
    } else {
      console.warn("No valid files in drop");
    }
  };

  const [platforms, setPlatforms] = useState([]);
  const userData = JSON.parse(localStorage.getItem("userdata")) || {};

  const getUserData = async () => {
    try {
      const response = await apiInstance.get(API_URL.GET_THIRDPARTY, {
        headers: {
          brand: selectedBrand?.id,
        },
      });
      if (response?.data?.status) {
        const statusData = response?.data?.data;
        setChannelsThirdParty(statusData);
      }
    } catch (error) {
      console.error("Error checking third-party status:", error);
    }
  };

  useEffect(() => {
    setChannelsThirdParty({});
    getUserData();
  }, [selectedBrand?.id]); // Add selectedBrand.id as a dependency

  useEffect(() => {
    if (open) {
      console.log("channelsThirdParty =======> ", channelsThirdParty);
      const connectedPlatforms = Object.entries(channelsThirdParty)
        .filter(([_, value]) => value === true)
        .map(([key]) => key);
      console.log(connectedPlatforms, "<======== connectedPlatforms");

      const updatedPlatforms =
        siteConstant?.CHANNEL_LIST?.map((platform) => ({
          ...platform,
          selected: false,
          disabled: !connectedPlatforms.includes(
            platform.name.toLowerCase() // Make comparison case-insensitive
          ),
        })) || [];

      setPlatforms(updatedPlatforms);
      setshareChecked(connectedPlatforms.length > 0);
    }
  }, [open, selectedBrand, channelsThirdParty]);

  const brandColors = {
    LinkedIn: "#0A66C2",
    Instagram: "#C30FB2",
    YouTube: "#FF0302",
    Pinterest: "#E60019",
    Vimeo: "#1EB8EB",
    reddit: "#FC471E",
    threads: "#100E0F",
    tiktok: "#000000",
    Twitter: "#1DA1F2",
    Facebook: "#1877F2",
    tumblr: "#35465C",
    Dailymotion: "#C5D1E3",
    Mastodon: "#6364FF"
  };

  const handleShareAll = (checked) => {
    let updatedPlatforms = platforms.map((platform) => {
      if (!platform.disabled) {
        return { ...platform, selected: checked };
      }
      return { ...platform, selected: false };
    });
    setshareChecked(checked);

    setPlatforms(updatedPlatforms);
  };

  const handleToggle = (platformId) => {
    // Find the platform object
    const platform = platforms.find((p) => p.id === platformId);
    if (!platform) return;

    // Get supported types for this platform
    const supportedTypes =
      siteConstant.PLATFORM_FILE_TYPE_SUPPORT[platform.name];
    if (!supportedTypes) {
      setApiMessage(
        "error",
        `Platform type support info missing for ${platform.name}`
      );
      return;
    }

    // Check all uploaded files
    let unsupported = false;
    for (let file of uploadedFile) {
      let type = null;
      if (file.type.startsWith("image/")) type = "image";
      else if (file.type.startsWith("video/")) type = "video";
      else type = null;
      if (!supportedTypes.includes(type)) {
        unsupported = true;
        break;
      }
    }
    if (unsupported) {
      setApiMessage(
        "error",
        ` ${platform.name} does not support the current media format. Please choose a platform that is compatible with your content type.`
      );
      return;
    }

    // If all files are supported, toggle as usual
    setPlatforms((prevChannels) =>
      prevChannels.map((platform) => {
        const updatedPlatform =
          platform?.id === platformId
            ? {
                ...platform,
                selected: !platform.selected,
                status: platform.selected ? "Disconnect" : "Connect",
              }
            : platform;
        return updatedPlatform;
      })
    );
  };

  const hasOnlyOneVideo = () => {
    return (
      uploadedFile.length === 1 && uploadedFile[0]?.type.startsWith("video/")
    );
  };
  const hasMixedContent = () => {
    if (uploadedFile.length <= 1) return false;
    const hasVideo = uploadedFile.some((file) =>
      file?.type.startsWith("video/")
    );
    const hasImage = uploadedFile.some((file) =>
      file?.type.startsWith("image/")
    );
    return hasVideo && hasImage;
  };

  const isVideoLongerThan90Seconds = () => {
    if (uploadedFile.length !== 1) return false;
    const file = uploadedFile[0];
    if (!file || !file.type.startsWith("video/")) return false;
    return videoDuration > 90;
  };

  return (
    <>
      <Dialog
        open={open && !uploaded}
        onClose={handleDialogClose}
        className="fixed inset-0 z-10 flex items-center justify-center"
      >
        <div
          className="fixed inset-1 bg-[#4B556363] transition-opacity"
          aria-hidden="true"
          onClick={handleBackdropClick}
        ></div>
        <div
          className="relative bg-white rounded-3xl overflow-hidden shadow-xl transform transition-all sm:max-w-lg sm:w-full p-8"
          onClick={(e) => e.stopPropagation()}
        >
          <div
            className={`border border-5 border-gray-400 border-dashed rounded-xl p-6 cursor-pointer font-Ubuntu ${
              isDragging && "border-Red border-2"
            }`}
          >
            <div
              className="bg-white px-4 pt-5 pb-2 sm:p-6 sm:pb-4"
              onDragEnter={handleDragEnter}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
              onClick={handleFileInputClick}
            >
              <div className="sm:flex sm:items-center sm:justify-center">
                <div className="">
                  <p className="sm:text-3xl font-bold text-center pb-5 text-gray-900">
                    {localesData?.USER_WEB?.CREATE_NEW_POST}
                  </p>
                  <div className="mt-2 flex flex-col justify-center items-center">
                    <img src={postimg} alt="Post" className="w-24 h-24" />
                    <p className="text-[12px] sm:text-sm mt-4 font-semibold whitespace-nowrap">
                      {localesData?.USER_WEB?.DRAG_PHOTOS_AND_VIDEOS_HERE}
                    </p>
                  </div>
                </div>
              </div>
            </div>
            <div className="px-4 py-3 sm:px-4 flex justify-center">
              <label
                htmlFor="upload-input"
                className="bg-Red px-4 py-2.5 sm:py-3 rounded-xl text-white text-[12px] sm:text-sm font-semibold cursor-pointer whitespace-nowrap"
              >
                {localesData?.USER_WEB?.SELECT_YOUR_PHOTOS}
              </label>
              <input
                id="upload-input"
                type="file"
                accept="image/jpeg,image/jpg,image/png,image/webp,image/bmp,image/tiff,image/svg+xml,video/*"
                ref={fileInputRef}
                style={{ display: "none" }}
                onChange={handleFileChange}
                multiple
              />
            </div>
          </div>
        </div>
      </Dialog>

      {uploaded && (
        <Dialog
          open={uploaded}
          onClose={handleDialogClose}
          className="fixed inset-0 z-10 flex items-center justify-center overflow-y-auto  "
        >
          <div
            className="fixed inset-0 flex justify-center items-center bg-gray-900 bg-opacity-50 overflow-y-auto "
            aria-hidden="true"
            onClick={handleBackdropClick}
          >
            {disabledMessage && (
              <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-2 rounded mb-4 text-center absolute top-2 right-2">
                {disabledMessage}
              </div>
            )}
            <Formik
              initialValues={{
                title: "",
                description: "",
                location: "",
                tagged_in: [],
                audience: false,
                schedule: moment().local(),
                facebook: false,
                instagram: false,
                linkedin: false,
                pinterest: false,
                vimeo: false,
                youtube: false,
                dailymotion: false,
                twitter: false,
                tumblr: false,
                reddit: false,
                tiktok: false,
                threads: false,
              }}
              onSubmit={async (values, { setSubmitting }) => {
                const isPost = formActionRef.current === "post";
                isPost ? setLoading(true) : setLoadingDraft(true);
                console.log(selectedIDs, "main");
                console.log(selectedNames, "mainName");
                setSelectedIDs([]);
                setDataDis([]);
                setDataTag([]);
                dispatch(selectedUser(selectedNames));
                try {
                  const form = new FormData();
                  form.append("title", values?.title || "''");
                  form.append("description", values?.description || "");
                  form.append("location", values?.location || "");
                  form.append("is_private", values?.audience);
                  form.append("is_text_post", "false");
                  form.append(
                    "is_posted",
                    formActionRef.current === "post" ? "true" : "false"
                  );
                  platforms?.filter(
                    (platform) => platform.name == "reddit"
                  )?.[0]?.selected && form.append("reddit", "True");
                  platforms?.filter(
                    (platform) => platform.name == "tumblr"
                  )?.[0]?.selected && form.append("tumblr", "True");
                  platforms?.filter(
                    (platform) => platform.name == "Facebook"
                  )?.[0]?.selected && form.append("facebook", "True");
                  platforms?.filter(
                    (platform) => platform.name == "Instagram"
                  )?.[0]?.selected && form.append("instagram", "True");
                  platforms?.filter(
                    (platform) => platform.name == "LinkedIn"
                  )?.[0]?.selected && form.append("linkedin", "True");
                  platforms?.filter(
                    (platform) => platform.name == "Pinterest"
                  )?.[0]?.selected && form.append("pinterest", "True");
                  platforms?.filter((platform) => platform.name == "Vimeo")?.[0]
                    ?.selected && form.append("vimeo", "True");
                  platforms?.filter(
                    (platform) => platform.name == "YouTube"
                  )?.[0]?.selected && form.append("youtube", "True");
                  platforms?.filter(
                    (platform) => platform.name == "Dailymotion"
                  )?.[0]?.selected && form.append("dailymotion", "True");
                  platforms?.filter((platform) => platform.name == "x")?.[0]
                    ?.selected && form.append("x", "True");
                  platforms?.filter(
                    (platform) => platform.name == "tiktok"
                  )?.[0]?.selected && form.append("tiktok", "True");
                  platforms?.filter(
                    (platform) => platform.name == "Threads"
                  )?.[0]?.selected && form.append("twitter", "True");
                  platforms?.filter((platform) => platform.name == "x")?.[0]
                    ?.selected && form.append("x", "True");
                     platforms?.filter(
                  (platform) => platform.name == "Mastodon")?.[0]
                    ?.selected && form.append("mastadon", "True");
                  if (selectedIDs.length > 0) {
                    form.append("tagged_in", JSON.stringify(selectedIDs));
                  } else {
                    form.append("tagged_in", "[]");
                  }

                  form.append(
                    "scheduled_at",
                    startDate
                      ? moment(startDate).format("YYYY-MM-DD HH:mm:ss.SSS")
                      : "" // Send empty string if no date selected
                  );
                  // Debug: Log files being uploaded
                  console.log("Files to upload:", {
                    uploadedChunkFile: uploadedChunkFile.length,
                    uploadedFile: uploadedFile.length,
                    thumbnailFiles: thumbnailFiles.length,
                  });

                  if (uploadedChunkFile.length) {
                    for (let file of uploadedChunkFile) {
                      console.log("Appending chunk file:", {
                        name: file.name,
                        type: file.type,
                        size: file.size,
                      });
                      form.append("upload_files", file);
                    }
                  } else {
                    for (let file of uploadedFile) {
                      console.log("Appending file:", {
                        name: file.name,
                        type: file.type,
                        size: file.size,
                        lastModified: file.lastModified,
                      });

                      // Special handling for GIF files
                      if (file.type === "image/gif") {
                        console.log(
                          "Processing GIF file for upload:",
                          file.name
                        );

                        // Verify the file is valid before appending
                        if (file && file.size > 0) {
                          try {
                            // Create a clean GIF file to ensure proper encoding
                            const cleanGifFile = await createCleanGifFile(file);

                            console.log("Appending clean GIF file:", {
                              name: cleanGifFile.name,
                              type: cleanGifFile.type,
                              size: cleanGifFile.size,
                              constructor: cleanGifFile.constructor.name,
                            });

                            // Append the clean GIF file
                            form.append(
                              "upload_files",
                              cleanGifFile,
                              cleanGifFile.name
                            );
                          } catch (error) {
                            console.error("Error processing GIF file:", error);
                            setApiMessage(
                              "error",
                              `Failed to process GIF file: ${file.name}`
                            );
                            return;
                          }
                        } else {
                          console.error("Invalid GIF file detected:", file);
                          setApiMessage(
                            "error",
                            `Invalid GIF file: ${file.name}`
                          );
                          return;
                        }
                      } else {
                        form.append("upload_files", file);
                      }
                    }
                  }

                  // Add thumbnail files if videos are present
                  if (thumbnailFiles.length > 0) {
                    for (let thumbnailFile of thumbnailFiles) {
                      console.log("Appending thumbnail file:", {
                        name: thumbnailFile.name,
                        type: thumbnailFile.type,
                        size: thumbnailFile.size,
                      });
                      form.append("thumbnail_files", thumbnailFile);
                    }
                  }

                  // Debug: Log FormData contents and validate file data
                  console.log("FormData entries:");
                  let hasGifFiles = false;
                  for (let [key, value] of form.entries()) {
                    if (value instanceof File) {
                      console.log(`${key}:`, {
                        name: value.name,
                        type: value.type,
                        size: value.size,
                        lastModified: value.lastModified,
                        constructor: value.constructor.name,
                      });

                      // Special validation for GIF files
                      if (value.type === "image/gif") {
                        hasGifFiles = true;
                        console.log(
                          "GIF file detected in FormData:",
                          value.name
                        );

                        // Try to read a small portion of the file to verify it's not corrupted
                        const reader = new FileReader();
                        reader.onload = (e) => {
                          const arrayBuffer = e.target.result;
                          const uint8Array = new Uint8Array(arrayBuffer);
                          console.log(
                            "GIF file header bytes:",
                            Array.from(uint8Array.slice(0, 10))
                          );

                          // Check for GIF signature (GIF87a or GIF89a)
                          const signature = String.fromCharCode(
                            ...uint8Array.slice(0, 6)
                          );
                          console.log("GIF signature:", signature);
                          if (!signature.startsWith("GIF")) {
                            console.error("Invalid GIF signature detected!");
                          }
                        };
                        reader.onerror = (error) => {
                          console.error(
                            "Error reading GIF file for validation:",
                            error
                          );
                        };
                        reader.readAsArrayBuffer(value.slice(0, 100)); // Read first 100 bytes
                      }
                    } else {
                      console.log(`${key}:`, value);
                    }
                  }

                  if (hasGifFiles) {
                    console.log(
                      "About to upload FormData containing GIF files"
                    );
                  }

                  // Prepare headers for upload
                  const uploadHeaders = {
                    brand: BrandId,
                    subscription: subscriptionId,
                    user: user,
                  };

                  // For GIF files, add specific content type header
                  if (hasGifFiles) {
                    console.log("Adding GIF-specific headers");
                    uploadHeaders["Accept"] = "application/json";
                    // Don't set Content-Type as it will be set automatically by FormData
                  }

                  console.log("Upload headers:", uploadHeaders);

                  // Final validation before upload
                  if (hasGifFiles) {
                    console.log("=== FINAL GIF VALIDATION BEFORE UPLOAD ===");
                    for (let [key, value] of form.entries()) {
                      if (value instanceof File && value.type === "image/gif") {
                        console.log("Final GIF file check:", {
                          name: value.name,
                          type: value.type,
                          size: value.size,
                          lastModified: value.lastModified,
                          isFile: value instanceof File,
                          isBlob: value instanceof Blob,
                          constructor: value.constructor.name,
                        });

                        // Try to read first few bytes to ensure file is not corrupted
                        try {
                          const slice = value.slice(0, 6);
                          const reader = new FileReader();
                          reader.onload = (e) => {
                            const bytes = new Uint8Array(e.target.result);
                            const signature = String.fromCharCode(...bytes);
                            console.log(
                              "GIF signature check:",
                              signature,
                              signature.startsWith("GIF")
                                ? "✓ VALID"
                                : "✗ INVALID"
                            );
                          };
                          reader.readAsArrayBuffer(slice);
                        } catch (error) {
                          console.error(
                            "Error validating GIF signature:",
                            error
                          );
                        }
                      }
                    }
                    console.log("=== END GIF VALIDATION ===");
                  }

                  const { status, data } = await apiInstance.post(
                    API_URL.UPLOAD_POST,
                    form,
                    {
                      headers: uploadHeaders,
                      timeout: 60000, // 60 second timeout for large GIF files
                    }
                  );
                  if (data) {
                    if (data?.status) {
                      if (data.data.is_scheduled) {
                        dispatch(scheduleData(data));
                      }
                      clearSelectedIDs();
                      setApiMessage("success", data?.message);
                      if (window.location.href.includes("profile")) {
                        dispatch(fetchProfile());
                      }
                    } else {
                      setApiMessage("error", data?.message);
                    }
                  } else {
                    setApiMessage(
                      "error",
                      localesData?.USER_WEB?.UPLOAD_FAILED
                    );
                  }
                  setSubmitting(false);
                } catch (error) {
                  console.error("Upload error details:", {
                    message: error?.message,
                    response: error?.response,
                    data: error?.response?.data,
                    status: error?.response?.status,
                    statusText: error?.response?.statusText,
                  });

                  // Check if this is the specific GIF decode error
                  if (
                    error?.response?.data?.message?.includes("NoneType") &&
                    error?.response?.data?.message?.includes("decode")
                  ) {
                    console.error(
                      "GIF decode error detected - this is likely a backend issue with file processing"
                    );
                    setApiMessage(
                      "error",
                      "GIF file upload failed. The file may be corrupted or the backend cannot process this GIF format."
                    );
                  } else {
                    setApiMessage(
                      "error",
                      error?.response?.data?.message ||
                        error?.message ||
                        "Upload failed"
                    );
                  }
                } finally {
                  isPost ? setLoading(false) : setLoadingDraft(false);
                }
                console.log("createpost", values);
                setTimeout(() => {
                  setSubmitting(false);
                  handleDialogClose();
                }, 400);
              }}
            >
              {({
                errors,
                handleBlur,
                handleChange,
                handleSubmit,
                touched,
                values,
                setFieldValue,
                isSubmitting,
              }) => (
                <Form
                  onSubmit={handleSubmit}
                  onKeyDown={(e) => {
                    if (e.key === "Enter") {
                      e.preventDefault();
                    }
                  }}
                  className="space-y-2.5 font-Ubuntu"
                >
                  <div className="bg-white rounded-[12px] overflow-y-auto max-w-[1200px] w-full max-h-[90vh] xl:h-[1100px] shadow-xl p-6 sm:p-8 md:p-10 my-6 sm:my-8 md:my-10 custom-scrollbar">
                    {/* Header with close button */}
                    <div className="relative ">
                      <h2 className="text-center pb-4 font-normal text-[22px]">
                        Create New Post
                      </h2>
                      <button
                        onClick={handleDialogClose}
                        className="absolute top-2 right-3"
                      >
                        <svg
                          width="18px"
                          height="18px"
                          viewBox="0 0 16 16"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M12 4L4 12M4 4L12 12"
                            stroke="#A9ABAD"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                        </svg>
                      </button>
                    </div>

                    {/* Main content */}
                    <div className="flex flex-row justify-center items-center">
                      {/* Image display */}
                      <div className="w-full md:w-1/2 md:mt-4 sm:pe-5 relative upload-img">
                        {images.length > 1 ? (
                          <Slider
                            {...settings}
                            ref={topSliderRef}
                            className="main-slider mb-4"
                          >
                            {images.map((item, index) => (
                              <div
                                key={index}
                                className="justify-center text-center"
                              >
                                {item?.is_video ? (
                                  item.hasOwnProperty("startTime") ? (
                                    <VideoPlayer
                                      videoData={{
                                        ...item,
                                        url: getSecureUrl(
                                          item.url || item.blobUrl
                                        ),
                                      }}
                                      localesData={localesData}
                                    />
                                  ) : (
                                    (() => {
                                      const fileUrl = getSecureUrl(
                                        item.url || item.blobUrl
                                      );
                                      const isMovFileType = item.originalFile
                                        ? isMovFile(item.originalFile)
                                        : false;
                                      const extension = item.originalFile
                                        ? getFileExtension(
                                            item.originalFile.name
                                          )
                                        : "";

                                      return (
                                        <video
                                          controls
                                          className="md:w-[340px] w-[250px] md:h-[360px] h-[250px] max-h-[440px] inline-block rounded-[20px] object-contain mx-auto"
                                          onError={(e) => {
                                            console.error(
                                              "Error loading video:",
                                              e
                                            );

                                            // For MOV files, try multiple approaches
                                            if (isMovFileType) {
                                              const videoElement = e.target;
                                              const currentSource =
                                                videoElement.querySelector(
                                                  "source"
                                                );

                                              if (currentSource) {
                                                // Try different MIME types for MOV
                                                const mimeTypes = [
                                                  "video/quicktime",
                                                  "video/x-quicktime",
                                                  "video/mp4",
                                                  "video/mov",
                                                ];

                                                let currentIndex =
                                                  mimeTypes.indexOf(
                                                    currentSource.type
                                                  );
                                                if (currentIndex === -1)
                                                  currentIndex = 0;

                                                const nextIndex =
                                                  (currentIndex + 1) %
                                                  mimeTypes.length;
                                                currentSource.type =
                                                  mimeTypes[nextIndex];

                                                console.log(
                                                  `Retrying MOV with MIME type: ${mimeTypes[nextIndex]}`
                                                );
                                                videoElement.load();
                                              }
                                            } else {
                                              // Try fallback URLs in sequence for non-MOV files
                                              if (item.blobUrl) {
                                                e.target.src = item.blobUrl;
                                              } else if (item.originalFile) {
                                                e.target.src =
                                                  URL.createObjectURL(
                                                    item.originalFile
                                                  );
                                              }
                                            }
                                          }}
                                        >
                                          <source
                                            src={fileUrl}
                                            type={(() => {
                                              if (isMovFileType) {
                                                // Try video/quicktime first for MOV files
                                                return "video/quicktime";
                                              }

                                              // Handle other video formats
                                              const mimeTypes = {
                                                mp4: "video/mp4",
                                                webm: "video/webm",
                                                avi: "video/x-msvideo",
                                              };
                                              return (
                                                mimeTypes[extension] ||
                                                `video/${extension}`
                                              );
                                            })()}
                                          />
                                          <source
                                            src={fileUrl}
                                            type="video/mp4"
                                          />
                                          {
                                            localesData?.USER_WEB
                                              ?.YOUR_BROWSER_DOES_NOT_SUPPORT_THE_VIDEO_TAG
                                          }
                                        </video>
                                      );
                                    })()
                                  )
                                ) : (
                                  <img
                                    src={getSecureUrl(item.url)}
                                    alt={`Media ${index + 1}`}
                                    className="md:w-[340px] w-[250px] md:h-[360px] h-[250px] inline-block rounded-[20px] object-cover mx-auto"
                                    onError={(e) => {
                                      console.error("Error loading image:", e);
                                    }}
                                  />
                                )}
                              </div>
                            ))}
                          </Slider>
                        ) : (
                          images.length === 1 && (
                            <div className="justify-center text-center">
                              {images[0]?.is_video ? (
                                (() => {
                                  const fileUrl = getSecureUrl(
                                    images[0].url || images[0].blobUrl
                                  );
                                  const isMovFileType = images[0].originalFile
                                    ? isMovFile(images[0].originalFile)
                                    : false;
                                  const extension = images[0].originalFile
                                    ? getFileExtension(
                                        images[0].originalFile.name
                                      )
                                    : "";

                                  return (
                                    <video
                                      controls
                                      className="md:w-[340px] w-[250px] md:h-[360px] h-[250px] max-h-[440px] inline-block rounded-2xl object-contain mx-auto"
                                      onError={(e) => {
                                        console.error(
                                          "Error loading single video:",
                                          e
                                        );

                                        // For MOV files, try multiple approaches
                                        if (isMovFileType) {
                                          const videoElement = e.target;
                                          const currentSource =
                                            videoElement.querySelector(
                                              "source"
                                            );

                                          if (currentSource) {
                                            // Try different MIME types for MOV
                                            const mimeTypes = [
                                              "video/quicktime",
                                              "video/x-quicktime",
                                              "video/mp4",
                                              "video/mov",
                                            ];

                                            let currentIndex =
                                              mimeTypes.indexOf(
                                                currentSource.type
                                              );
                                            if (currentIndex === -1)
                                              currentIndex = 0;

                                            const nextIndex =
                                              (currentIndex + 1) %
                                              mimeTypes.length;
                                            currentSource.type =
                                              mimeTypes[nextIndex];

                                            console.log(
                                              `Retrying single MOV with MIME type: ${mimeTypes[nextIndex]}`
                                            );
                                            videoElement.load();
                                          }
                                        }
                                      }}
                                    >
                                      <source
                                        src={fileUrl}
                                        type={(() => {
                                          if (isMovFileType) {
                                            // Try video/quicktime first for MOV files
                                            return "video/quicktime";
                                          }

                                          // Handle other video formats
                                          const mimeTypes = {
                                            mp4: "video/mp4",
                                            webm: "video/webm",
                                            avi: "video/x-msvideo",
                                          };
                                          return (
                                            mimeTypes[extension] ||
                                            `video/${extension}`
                                          );
                                        })()}
                                      />
                                      <source src={fileUrl} type="video/mp4" />
                                      {
                                        localesData?.USER_WEB
                                          ?.YOUR_BROWSER_DOES_NOT_SUPPORT_THE_VIDEO_TAG
                                      }
                                    </video>
                                  );
                                })()
                              ) : (
                                <img
                                  src={getSecureUrl(images[0].url)}
                                  alt="Media 1"
                                  className="md:w-[340px] w-[250px] md:h-[360px] h-[250px] inline-block rounded-[30px] object-cover mx-auto"
                                />
                              )}
                            </div>
                          )
                        )}
                        <div className="px-7 md:px-0 lg:px-[7px] mt-4">
                          <Slider {...bottomsettings}>
                            {images.map((item, index) => (
                              <div
                                key={index}
                                className="relative group cursor-pointer flex justify-center items-center mx-1 mb-0 md:mb-2"
                                onClick={() => handleThumbnailClick(index)}
                              >
                                {item?.is_video ? (
                                  <div className="relative w-full h-full">
                                    <video
                                      className="w-[50px] h-[50px] sm:w-[60px] sm:h-[60px] md:w-[70px] md:h-[70px] rounded-[8px] object-cover"
                                      preload="metadata"
                                      muted
                                      playsInline
                                      controls={false}
                                      onError={(e) => {
                                        console.error(
                                          "Error loading video preview:",
                                          e
                                        );

                                        const isMovFileType = item.originalFile
                                          ? isMovFile(item.originalFile)
                                          : false;

                                        // For MOV files, try multiple approaches
                                        if (isMovFileType) {
                                          const videoElement = e.target;
                                          const currentSource =
                                            videoElement.querySelector(
                                              "source"
                                            );

                                          if (currentSource) {
                                            // Try different MIME types for MOV
                                            const mimeTypes = [
                                              "video/quicktime",
                                              "video/x-quicktime",
                                              "video/mp4",
                                              "video/mov",
                                            ];

                                            let currentIndex =
                                              mimeTypes.indexOf(
                                                currentSource.type
                                              );
                                            if (currentIndex === -1)
                                              currentIndex = 0;

                                            const nextIndex =
                                              (currentIndex + 1) %
                                              mimeTypes.length;
                                            currentSource.type =
                                              mimeTypes[nextIndex];

                                            console.log(
                                              `Retrying thumbnail MOV with MIME type: ${mimeTypes[nextIndex]}`
                                            );
                                            videoElement.load();
                                          }
                                        } else {
                                          // Try fallback URLs in sequence for non-MOV files
                                          if (item.blobUrl) {
                                            e.target.src = item.blobUrl;
                                          } else if (item.originalFile) {
                                            e.target.src = URL.createObjectURL(
                                              item.originalFile
                                            );
                                          } else {
                                            e.target.style.display = "none";
                                            const fallbackDiv =
                                              document.createElement("div");
                                            fallbackDiv.className =
                                              "w-[50px] h-[50px] sm:w-[60px] sm:h-[60px] md:w-[70px] md:h-[70px] rounded-[8px] bg-gray-200 flex items-center justify-center";
                                            fallbackDiv.innerHTML =
                                              '<svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" /></svg>';
                                            e.target.parentNode.appendChild(
                                              fallbackDiv
                                            );
                                          }
                                        }
                                      }}
                                    >
                                      <source
                                        src={
                                          item.url
                                            ? getSecureUrl(item.url)
                                            : URL.createObjectURL(item)
                                        }
                                        type={(() => {
                                          const isMovFileType =
                                            item.originalFile
                                              ? isMovFile(item.originalFile)
                                              : false;
                                          if (isMovFileType) {
                                            // Try video/quicktime first for MOV files
                                            return "video/quicktime";
                                          }

                                          // Handle other video formats
                                          const extension = item.originalFile
                                            ? getFileExtension(
                                                item.originalFile.name
                                              )
                                            : "";
                                          const mimeTypes = {
                                            mp4: "video/mp4",
                                            webm: "video/webm",
                                            avi: "video/x-msvideo",
                                          };
                                          return (
                                            mimeTypes[extension] ||
                                            `video/${extension}`
                                          );
                                        })()}
                                      />
                                      <source
                                        src={
                                          item.url
                                            ? getSecureUrl(item.url)
                                            : URL.createObjectURL(item)
                                        }
                                        type="video/mp4"
                                      />
                                    </video>
                                  </div>
                                ) : (
                                  <img
                                    src={getSecureUrl(item.url)}
                                    alt={`Thumbnail ${index + 1}`}
                                    className="w-[50px] h-[50px] sm:w-[60px] sm:h-[60px] md:w-[70px] md:h-[70px] rounded-[8px] object-cover"
                                    onError={(e) => {
                                      console.error(
                                        "Error loading thumbnail:",
                                        e
                                      );
                                    }}
                                  />
                                )}

                                {/* Close Button */}
                                <div
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleDeleteImage(index);
                                  }}
                                  className="absolute top-0 right-[2vh] sm:top-0 sm:right-1 md:top-0 md:right-[1vh] lg:top-0 lg:right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-200 "
                                >
                                  <div className="flex justify-center items-center rounded-full p-1 cursor-pointer bg-gray-600">
                                    <IoMdClose className="h-3 w-3 text-white" />
                                  </div>
                                </div>
                              </div>
                            ))}
                            {images?.length <= 9 && (
                              <div className="mb-1 ms-1">
                                <label
                                  htmlFor="upload"
                                  className={`cursor-pointer whitespace-nowrap flex justify-center items-center bg-profileCardBG w-[50px] h-[50px] sm:w-[60px] sm:h-[60px] md:w-[70px] md:h-[70px] rounded-[8px] mb-0 md:mb-2 ${
                                    firstMediaType ? "relative" : ""
                                  }`}
                                >
                                  <div className="flex justify-center items-center cursor-pointer h-full w-full">
                                    <FiPlus className="h-5 w-5 text-gray-500" />
                                  </div>
                                </label>
                                <input
                                  id="upload"
                                  type="file"
                                  onChange={handleFileChange}
                                  ref={fileInputRef}
                                  className="hidden"
                                  accept={
                                    firstMediaType === "video"
                                      ? "video/*"
                                      : firstMediaType === "image"
                                      ? "image/jpeg,image/jpg,image/png,image/webp,image/bmp,image/tiff,image/svg+xml"
                                      : "image/jpeg,image/jpg,image/png,image/webp,image/bmp,image/tiff,image/svg+xml,video/*"
                                  }
                                  multiple
                                />
                              </div>
                            )}
                          </Slider>
                        </div>
                      </div>

                      {/* Form Fields */}
                      <div className="p-4">
                        {/* Title */}
                        <div className="mb-3">
                          <input
                            type="text"
                            id="title"
                            name="title"
                            placeholder="Title"
                            className="w-full p-2 border-[1px] border-[#563D3926] rounded-[10px] focus:outline-none"
                            value={values.title}
                            onChange={handleChange}
                            onBlur={handleBlur}
                          />
                        </div>

                        {/* Description */}
                        <div className="mb-3">
                          <textarea
                            id="description"
                            name="description"
                            placeholder="Description"
                            className="w-full p-2 border-[1px] border-[#563D3926] rounded-[10px] focus:outline-none resize-none"
                            rows="3"
                            value={values.description}
                            onChange={(e) =>
                              setFieldValue("description", e.target.value)
                            }
                            onBlur={handleBlur}
                          ></textarea>
                        </div>

                        {/* Only show video chunking option if there's exactly one video, no images, and video is longer than 90 seconds */}
                        {hasOnlyOneVideo() &&
                          !hasMixedContent() &&
                          isVideoLongerThan90Seconds() && (
                            <div className="flex justify-between items-center font-Ubuntu font-semibold my-4">
                              <Typography
                                variant="subtitle1"
                                sx={{
                                  fontFamily: "Ubuntu, sans-serif",
                                  fontWeight: "700",
                                  color: "#674941",
                                }}
                              >
                                {localesData?.USER_WEB?.VIDEO_CHUNKING ||
                                  "Video Chunking"}
                              </Typography>
                              <FormGroup>
                                <FormControlLabel
                                  control={
                                    <Android12Switch
                                      checked={isVideoChunkingEnabled}
                                      onChange={(e) =>
                                        handleToggleChunk(e.target.checked)
                                      }
                                      disabled={isChunkingInProgress}
                                    />
                                  }
                                  label=""
                                />
                              </FormGroup>
                            </div>
                          )}

                        {/* Chunking Status and Loading Indicator */}
                        {isVideoChunkingEnabled &&
                          hasOnlyOneVideo() &&
                          !hasMixedContent() &&
                          isVideoLongerThan90Seconds() && (
                            <div className="mt-2">
                              {isChunkingInProgress && (
                                <div className="flex justify-center items-center mt-2">
                                  <Spinner />
                                </div>
                              )}
                            </div>
                          )}

                        {chunkingError && (
                          <div className="text-red-500 text-sm mt-2">
                            {chunkingError}
                          </div>
                        )}

                        {/* Action buttons */}
                        <div className="flex justify-center items-center space-x-3 py-2">
                          <button
                            type="button"
                            className="flex items-center justify-center p-2"
                            onClick={() => setShowLocationPopup(true)}
                          >
                            <img
                              src={locationIcon}
                              alt="Location"
                              className="w-[40px] h-[40px] text-gray-600}"
                            />
                          </button>
                          <button
                            type="button"
                            className="flex items-center justify-center p-2"
                            onClick={() => setShowTagPopup(true)}
                          >
                            <img
                              src={tagIcon}
                              alt="Location"
                              className="w-[40px] h-[40px] text-gray-600}"
                            />
                          </button>
                          <div className="relative flex items-center">
                            <button
                              type="button"
                              className="flex items-center justify-center p-2"
                              onClick={() => setShowDatePicker(!showDatePicker)}
                            >
                              <img
                                src={scheduleIcon}
                                alt="Schedule"
                                className="w-[40px] h-[40px]"
                              />
                            </button>

                            {showDatePicker && (
                              <div
                                ref={datePickerRef}
                                className="absolute z-50 overflow-hidden"
                                style={{
                                  top: "-400px",
                                  right: "-90px",
                                  width: "328.6px",
                                  height: "350px",
                                }}
                              >
                                <DatePicker
                                  selected={tempDate}
                                  onChange={(date) => setTempDate(date)}
                                  showTimeSelect
                                  timeFormat="hh:mm aa"
                                  timeIntervals={1} // allow any minute
                                  minDate={new Date()} // Disable all past dates
                                  minTime={
                                    tempDate &&
                                    new Date(tempDate).toDateString() ===
                                      new Date().toDateString()
                                      ? (() => {
                                          // Set to now + 16 minutes
                                          const now = new Date();
                                          now.setSeconds(0, 0);
                                          now.setMinutes(now.getMinutes() + 16);
                                          return now;
                                        })()
                                      : new Date(
                                          new Date().setHours(0, 0, 0, 0)
                                        )
                                  }
                                  maxTime={
                                    new Date(new Date().setHours(23, 59, 0, 0))
                                  }
                                  inline
                                  selectsRange={false}
                                  popperPlacement="bottom-end"
                                  calendarClassName=""
                                  renderCustomHeader={({
                                    date,
                                    decreaseMonth,
                                    increaseMonth,
                                    prevMonthButtonDisabled,
                                    nextMonthButtonDisabled,
                                  }) => (
                                    <div className="flex items-center justify-between px-2 py-1">
                                      <button
                                        type="button"
                                        onClick={decreaseMonth}
                                        disabled={prevMonthButtonDisabled}
                                        className="p-2 hover:bg-gray-100 rounded"
                                      >
                                        ←
                                      </button>
                                      <span className="font-semibold">
                                        {date.toLocaleString("default", {
                                          month: "long",
                                          year: "numeric",
                                        })}
                                      </span>
                                      <button
                                        type="button"
                                        onClick={increaseMonth}
                                        disabled={nextMonthButtonDisabled}
                                        className="p-2 hover:bg-gray-100 rounded"
                                      >
                                        →
                                      </button>
                                    </div>
                                  )}
                                  renderCustomTime={({ handleTimeChange }) => {
                                    const baseDate = tempDate || new Date();
                                    return (
                                      <div className="border-l border-gray-200 w-32">
                                        <div className="px-3 py-2 bg-gray-50 border-b">
                                          <h3 className="text-sm font-medium text-gray-700 text-center">
                                            Time
                                          </h3>
                                        </div>
                                        <div className="h-60 overflow-y-auto">
                                          {[...Array(24).keys()].map((hour) =>
                                            [0, 15, 30, 45].map((minute) => {
                                              const timeValueInner = new Date(
                                                baseDate
                                              );
                                              timeValueInner.setHours(
                                                hour,
                                                minute,
                                                0,
                                                0
                                              );
                                              // Disable times before now if today is selected
                                              const isToday =
                                                new Date(
                                                  baseDate
                                                ).toDateString() ===
                                                new Date().toDateString();
                                              const now = new Date();
                                              let isDisabled = false;
                                              if (isToday) {
                                                // Round up now to next 15 min
                                                const roundedNow = new Date(
                                                  now
                                                );
                                                roundedNow.setSeconds(0, 0);
                                                const nowMinutes =
                                                  roundedNow.getMinutes();
                                                roundedNow.setMinutes(
                                                  nowMinutes +
                                                    ((16 - (nowMinutes % 16)) %
                                                      16)
                                                );
                                                isDisabled =
                                                  timeValueInner < roundedNow;
                                              }
                                              if (
                                                isNaN(timeValueInner.getTime())
                                              )
                                                return null;
                                              return (
                                                <button
                                                  type="button"
                                                  key={`${hour}-${minute}`}
                                                  onClick={() => {
                                                    if (!isDisabled) {
                                                      const newDate = new Date(
                                                        baseDate
                                                      );
                                                      newDate.setHours(
                                                        hour,
                                                        minute
                                                      );
                                                      setTempDate(newDate);
                                                      handleTimeChange(newDate);
                                                    }
                                                  }}
                                                  className={`w-full text-left text-sm p-2 hover:bg-gray-100 ${
                                                    baseDate.getHours() ===
                                                      hour &&
                                                    baseDate.getMinutes() ===
                                                      minute
                                                      ? "bg-[#674941] text-white"
                                                      : ""
                                                  } ${
                                                    isDisabled
                                                      ? "opacity-40 cursor-not-allowed"
                                                      : ""
                                                  }`}
                                                  disabled={isDisabled}
                                                >
                                                  {timeValueInner.toLocaleTimeString(
                                                    [],
                                                    {
                                                      hour: "2-digit",
                                                      minute: "2-digit",
                                                      hour12: true,
                                                    }
                                                  )}
                                                </button>
                                              );
                                            })
                                          )}
                                        </div>
                                      </div>
                                    );
                                  }}
                                />
                                <div className="flex justify-between items-center bg-gray-50 rounded-md">
                                  <p className="text-[15px] text-Red text-center">
                                    Schedule at least 15 mins ahead
                                  </p>
                                  <div className="flex justify-end p-4 ">
                                    <button
                                      type="button"
                                      onClick={() => {
                                        handleDateChange(null);
                                        setShowDatePicker(false);
                                      }}
                                      className="px-4 py-2 text-gray-600 hover:bg-gray-100 rounded"
                                    >
                                      Cancel
                                    </button>
                                    <button
                                      type="button"
                                      onClick={() => {
                                        handleDateChange(tempDate);
                                        setShowDatePicker(false);
                                      }}
                                      className="px-4 py-2 bg-[#674941] text-white rounded hover:opacity-90"
                                    >
                                      Done
                                    </button>
                                  </div>
                                </div>
                              </div>
                            )}
                          </div>
                        </div>

                        {/* Social Platform Section */}
                        <div className="">
                          <h3 className="font-semibold text-sm mb-4 mt-1 flex justify-center items-center">
                            Shared With Connected Platforms
                          </h3>
                          <div className="flex flex-wrap justify-center gap-3 my-4">
                            {platforms.map((platform) => {
                              const isSelected = platform.selected;
                              const platformKey = platformKeyMap[platform.name];
                              const isConnected = platformKey
                                ? channelsThirdParty[platformKey] === true
                                : false;
                              const color =
                                brandColors[platform.name] || "#D1D5DB";

                              return (
                                <div
                                  key={platform?.id}
                                  className={`flex items-center justify-center w-12 h-12 rounded-lg cursor-pointer transition-all duration-200 ${
                                    isSelected ? "" : "bg-gray-100"
                                  } ${
                                    !isConnected
                                      ? "opacity-50 cursor-not-allowed"
                                      : ""
                                  }`}
                                  style={
                                    isSelected
                                      ? {
                                          backgroundColor: `${color}1A`,
                                          border: `2px solid ${color}`,
                                        }
                                      : {}
                                  }
                                  onClick={() => {
                                    if (isConnected) handleToggle(platform?.id);
                                  }}
                                >
                                  <img
                                    src={platform.icon || ""}
                                    alt={platform.name || "Platform"}
                                    className="w-6 h-6"
                                  />
                                </div>
                              );
                            })}
                          </div>
                        </div>

                        {/* Post Button */}
                        <div className="mt-9 flex justify-center items-center gap-4 flex-wrap">
                          <button
                            type="submit"
                            disabled={loading}
                            onClick={() => (formActionRef.current = "post")}
                            className={`w-[130px] md:w-[140px] h-[40px] flex justify-center items-center px-4 py-2 font-semibold rounded-lg transition-colors duration-200 ${
                              loading
                                ? "bg-[#a38a83] cursor-not-allowed"
                                : "bg-[#674941] hover:bg-[#5a3d36]"
                            } text-white`}
                          >
                            {loading ? <Spinner /> : "Post"}
                          </button>
                          <button
                            type="button"
                            disabled={loading}
                            onClick={() => {
                              formActionRef.current = "draft";
                              handleSubmit();
                            }}
                            className={`w-[130px] md:w-[140px] h-[40px] flex justify-center items-center px-4 py-2 font-semibold rounded-lg transition-colors duration-200 ${
                              loading
                                ? "bg-[#a38a83] cursor-not-allowed"
                                : "bg-[#674941] hover:bg-[#5a3d36]"
                            } text-white`}
                          >
                            {loadingDraft ? <Spinner /> : "Save as Draft"}
                          </button>
                        </div>

                        {showLocationPopup && (
                          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                            <div className="bg-white rounded-lg p-6 w-96">
                              <div className="flex justify-between items-center mb-4">
                                <h3 className="text-lg font-semibold">
                                  Select Location
                                </h3>
                                <button
                                  onClick={() => setShowLocationPopup(false)}
                                  className="text-gray-500 hover:text-gray-700"
                                >
                                  <IoMdClose size={20} />
                                </button>
                              </div>
                              <input
                                type="text"
                                placeholder="Search locations..."
                                className="w-full p-2 border rounded mb-4"
                                value={searchText}
                                onChange={handleSearchChange}
                              />
                              {isLoading ? (
                                <Spinner />
                              ) : (
                                <ListGroup className="max-h-60 overflow-y-auto">
                                  {/* Show current location suggestions when search is empty */}
                                  {searchText === "" ? (
                                    currentLocationAPI.length > 0 ? (
                                      currentLocationAPI.map(
                                        (location, index) => (
                                          <ListGroupItem
                                            key={index}
                                            onClick={() => {
                                              setFieldValue(
                                                "location",
                                                location.suggested_place
                                              );
                                              setSearchText(
                                                location.suggested_place
                                              );
                                              setShowLocationPopup(false);
                                            }}
                                            className="cursor-pointer hover:bg-gray-100"
                                          >
                                            {location.suggested_place}
                                          </ListGroupItem>
                                        )
                                      )
                                    ) : (
                                      <ListGroupItem>
                                        {geolocationError ||
                                          "No current location available"}
                                      </ListGroupItem>
                                    )
                                  ) : locationResults.length > 0 ? (
                                    locationResults.map((location, index) => (
                                      <ListGroupItem
                                        key={index}
                                        onClick={() => {
                                          setFieldValue(
                                            "location",
                                            location.suggested_place
                                          );
                                          setSearchText(
                                            location.suggested_place
                                          );
                                          setShowLocationPopup(false);
                                        }}
                                        className="cursor-pointer hover:bg-gray-100"
                                      >
                                        {location.suggested_place}
                                      </ListGroupItem>
                                    ))
                                  ) : (
                                    <ListGroupItem>
                                      No results found
                                    </ListGroupItem>
                                  )}
                                </ListGroup>
                              )}
                            </div>
                          </div>
                        )}

                        {showTagPopup && (
                          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                            <div className="bg-white rounded-lg p-6 w-96">
                              <div className="flex justify-between items-center mb-4">
                                <h3 className="text-lg font-semibold">
                                  Tag People
                                </h3>
                                <button
                                  onClick={() => {
                                    setShowTagPopup(false);
                                    setSelectedIdTag([]);
                                    setCompareDateTag([]);
                                    setDataTag([]);
                                    setFieldValue &&
                                      setFieldValue("tagged_in", []);
                                  }}
                                  className="text-gray-500 hover:text-gray-700"
                                >
                                  <IoMdClose size={20} />
                                </button>
                              </div>
                              <TagInTextField
                                suggestionData={suggestionData}
                                value={values.tagged_in}
                                id="tagged_in"
                                name={"tagged_in"}
                                activeInput={activeInput}
                                setActiveInput={() => setActiveInput("tag")}
                                onChange={(value, selectedID) => {
                                  setFieldValue("tagged_in", value);
                                  if (selectedID) {
                                    mergeUniqueIDsForTag(selectedID);
                                  }
                                }}
                                selectedIDs={selectedIDTag}
                                setselectedIDs={setSelectedIdTag}
                                compareDataForRemove={compareDataTag}
                                removeTextTag={removeTextTag}
                                mainDataTag={dataTag}
                              />
                              <button
                                className="mt-4 w-full bg-Red text-white py-2 rounded-lg"
                                onClick={() => setShowTagPopup(false)}
                              >
                                Done
                              </button>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </Form>
              )}
            </Formik>
          </div>
        </Dialog>
      )}
    </>
  );
};
export default UploadPost;
