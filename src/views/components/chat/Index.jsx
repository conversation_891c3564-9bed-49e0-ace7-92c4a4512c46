import React, { useState, useEffect, useRef, useContext } from "react";
import Sidebar from "./Sidebar";
import ChatModule from "./ChatModule";
import apiInstance from "../../../helpers/Axios/axiosINstance";
import { URL } from "../../../helpers/constant/Url";
import { fetchFromStorage } from "../../../helpers/context/storage";
import siteConstant from "../../../helpers/constant/siteConstant";
import { SocketContext } from "../../../helpers/context/socket";
import facebook from "../../../assets/images/Chating/Facebook.svg";
import instagram from "../../../assets/images/Chating/Instragram.svg";
import flowkar from "../../../assets/images/Chating/flowkar.svg";
import meta from "../../../assets/images/svg_icon/meta.svg";
import Loader from "../../../helpers/UI/Loader";
import { useBrand } from "../../../helpers/context/BrandContext";

const Index = () => {
  // Add abortController ref to manage API requests
  const abortControllerRef = useRef(null);

  // Socket context for Flowkar messaging
  const socket = useContext(SocketContext);

  const [iconlist, setIconList] = useState([
    { id: "flowkar", name: "flowkar", icon: flowkar },
    { id: "all", name: "All Meta", icon: meta }, // Remove curly braces
    { id: "facebook", name: "Facebook", icon: facebook }, // Remove curly braces
    { id: "instagram", name: "Instagram", icon: instagram }, // Remove curly braces
  ]);

  const [activeChannel, setActiveChannel] = useState("flowkar");
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [isloading, setIsLoading] = useState(true);
  const [sloading, setSLoading] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [plateform, setPlateform] = useState(null);
  const [isMobile, setIsMobile] = useState(false);
  const [sidebarVisible, setSidebarVisible] = useState(true);
  const { selectedBrand } = useBrand();

  // Store messages per user ID to prevent cross-contamination
  const [messagesMap, setMessagesMap] = useState({});
  const [newMessage, setNewMessage] = useState("");
  const BrandId = localStorage.getItem("BrandId");
  const user = localStorage.getItem("UserId");
  const token = fetchFromStorage(siteConstant?.INDENTIFIERS?.USERDATA)?.token;
  const [file, setFile] = useState([]);
  const [msgloader, setMsgloader] = useState(false);
  const [isPolling, setIsPolling] = useState(false);

  // Track polling interval
  const pollingIntervalRef = useRef(null);

  // Last fetch timestamp to handle out-of-order responses
  const lastFetchTimestampRef = useRef({});

  // Track initial load status for each user
  const [initialLoadMap, setInitialLoadMap] = useState({});

  // Add a ref to track if component is mounted
  const isMountedRef = useRef(true);

  // Track pending markAllRead requests
  const pendingMarkAllReadRef = useRef({});

  // Pagination state management
  const [paginationState, setPaginationState] = useState({});
  const [isLoadingMore, setIsLoadingMore] = useState(false);

  // Get current messages for selected user

  const getCurrentMessages = () => {
    return selectedUser?.id ? messagesMap[selectedUser.id] || [] : [];
  };

  // Check if initial load is complete for current user
  const isInitialLoadComplete = () => {
    return selectedUser?.id ? initialLoadMap[selectedUser.id] || false : false;
  };

  // Get recipient ID based on selected user or messages
  const getRecipientId = () => {
    if (selectedUser?.ChatingUserId) {
      return selectedUser.ChatingUserId;
    }

    const messages = getCurrentMessages();
    // Find the first message where the user is the recipient
    const message = messages.find(
      (msg) =>
        msg.from?.username !== selectedUser?.name && msg.to?.data?.[0]?.id
    );

    return message?.to?.data?.[0]?.id || messages?.[0]?.to?.data?.[0]?.id;
  };

  // Function to cancel any pending requests - but only for user changes
  const cancelPendingRequests = (reason = "user_change") => {
    if (abortControllerRef.current && reason === "user_change") {
      console.log("Canceling pending requests due to:", reason);
      abortControllerRef.current.abort();
    }
    // Create a new controller for future requests
    abortControllerRef.current = new AbortController();
  };

  // Function to process pending markAllRead requests after messages are loaded
  const processPendingMarkAllRead = (userId, userMessages) => {
    if (pendingMarkAllReadRef.current[userId]) {
      console.log(`📖 Processing pending markAllRead for user ${userId}`);

      const currentUserId = parseInt(localStorage.getItem("UserId"));

      // Find unread messages from other users
      const unreadMessages = userMessages.filter(
        (msg) => !msg.is_read && msg.from?.id !== currentUserId
      );

      console.log(`📖 markAllRead for user ${userId}:`, {
        userId,
        totalMessages: userMessages.length,
        unreadMessages: unreadMessages.length,
        unreadMessageIds: unreadMessages.map((msg) => msg.id),
      });

      if (unreadMessages.length > 0) {
        // Update local state to mark all messages as read
        setMessagesMap((prevMap) => {
          const updatedMessages = (prevMap[userId] || []).map((msg) => ({
            ...msg,
            is_read: true,
          }));
          return {
            ...prevMap,
            [userId]: updatedMessages,
          };
        });

        // Emit message_read events for the unread messages
        unreadMessages.forEach((msg) => {
          const messageReadData = {
            Authorization: `Bearer ${token}`,
            to: currentUserId,
            message_id: msg.id,
          };
          if (socket && socket.connected) {
            socket.emit("message_read", messageReadData, (response) => {
              console.log("📖 Emitted message_read for:", msg.id, response);
            });
          }
        });
      }

      // Clear the pending request
      delete pendingMarkAllReadRef.current[userId];
    }
  };
  // Function to fetch flowkar chat list
  const fetchFlowkarUsers = async (isInitialLoad = false) => {
    // Only show loading spinner on initial load, not on refreshes
    if (isInitialLoad || users.length === 0) {
      setSLoading(true);
    }

    try {
      const response = await apiInstance.get(URL.CHAT_LIST, {
        headers: {
          brand: BrandId,
          user: user,
          Authorization: `Bearer ${token}`,
        },
      });
      if (
        response.data &&
        response.data.results &&
        response.data.results.status === true
      ) {
        const flowkarUsers = response.data.results.data.map((user) => ({
          id: user.user_id,
          ChatingUserId: user.user_id,
          name: user.name,
          user_name: user.user_name,
          owner: user.to_user,
          avatar: user.profile_image
            ? `https://api.flowkar.com${user.profile_image}`
            : null,
          status: "active",
          message: user.latest_message || "No messages",
          socialPlatform: "flowkar",
          unreadCount: user.unread_count || (user.is_read ? 0 : 1),
          created_at: user.created_at,
        }));

        // Gracefully update users - preserve real-time updates if they exist
        setUsers((prevUsers) => {
          // If we have previous users, merge the latest messages from real-time updates
          if (prevUsers.length > 0) {
            return flowkarUsers.map((newUser) => {
              const existingUser = prevUsers.find((u) => u.id === newUser.id);
              // If user exists and has a more recent message from real-time updates, keep it
              if (existingUser && existingUser.message !== newUser.message) {
                // Check if the existing message is more recent (from real-time updates)
                const existingTime = new Date(
                  existingUser.updated_at || existingUser.created_at || 0
                );
                const newTime = new Date(newUser.created_at || 0);
                if (existingTime > newTime) {
                  return {
                    ...newUser,
                    message: existingUser.message,
                    latest_message: existingUser.message,
                  };
                }
              }
              return newUser;
            });
          }
          return flowkarUsers;
        });

        setPlateform(3); // Set platform to 3 for flowkar

        console.log("✅ Flowkar users updated gracefully");
      }
    } catch (error) {
      console.error("Error fetching flowkar users:", error);
    } finally {
      setSLoading(false);
    }
  };

  const fetchUsers = async (channel, isInitialLoad = false) => {
    // Check if the selected channel is flowkar
    if (channel === "flowkar") {
      await fetchFlowkarUsers(isInitialLoad);
      return;
    }

    // Only show loading spinner on initial load, not on refreshes
    if (isInitialLoad || users.length === 0) {
      setSLoading(true);
    }

    try {
      const response = await apiInstance.get(URL.ALL_CHAT_LIST, {
        headers: {
          brand: BrandId,
          Authorization: `Bearer ${token}`,
        },
      });

      if (
        response.data &&
        response.data.status === true &&
        response.data.data
      ) {
        let usersToShow = [];

        const mapUsers = (users, platform) =>
          users.map((user) => ({
            id: user.conversation_id,
            ChatingUserId: user.chatting_user_id,
            name: user.name || `${platform} User`,
            owner: user.owner_id,
            avatar: user.profile_pic_url || null,
            status: "active",
            message: user.last_message || "No messages",
            socialPlatform: platform,
            unreadCount: user.unread_count || 0,
          }));

        if (channel === "All Meta") {
          const facebookUsers = mapUsers(
            response.data.data.facebook || [],
            "facebook"
          );
          const instagramUsers = mapUsers(
            response.data.data.instagram || [],
            "instagram"
          );

          usersToShow = [...facebookUsers, ...instagramUsers];
        } else if (channel === "Facebook") {
          setPlateform(1);
          usersToShow = mapUsers(response.data.data.facebook || [], "facebook");
        } else if (channel === "Instagram") {
          setPlateform(2);
          usersToShow = mapUsers(
            response.data.data.instagram || [],
            "instagram"
          );
        }

        // Gracefully update users - preserve real-time updates if they exist
        setUsers((prevUsers) => {
          // If we have previous users, merge the latest messages from real-time updates
          if (prevUsers.length > 0) {
            return usersToShow.map((newUser) => {
              const existingUser = prevUsers.find((u) => u.id === newUser.id);
              // If user exists and has a more recent message from real-time updates, keep it
              if (existingUser && existingUser.message !== newUser.message) {
                // Check if the existing message is more recent (from real-time updates)
                const existingTime = new Date(
                  existingUser.updated_at || existingUser.created_at || 0
                );
                const newTime = new Date(newUser.created_at || 0);
                if (existingTime > newTime) {
                  return {
                    ...newUser,
                    message: existingUser.message,
                    latest_message: existingUser.message,
                  };
                }
              }
              return newUser;
            });
          }
          return usersToShow;
        });

        console.log("✅ Users updated gracefully for channel:", channel);
      }
    } catch (error) {
      console.error("Error fetching users:", error);
    } finally {
      setSLoading(false);
    }
  };

  // Enhanced handleUserSelect to support markAllRead option
  const handleUserSelect = (user, options = {}) => {
    // Store markAllRead option for later use after messages are loaded
    if (options.markAllRead) {
      // Store the markAllRead request for this user
      pendingMarkAllReadRef.current[user.id] = true;
      console.log(`📖 Queued markAllRead for ${user.name} (ID: ${user.id})`);
    }

    // Only cancel requests when actually changing users
    if (!selectedUser || user.id !== selectedUser.id) {
      cancelPendingRequests("user_change");

      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current);
      }

      // Batch state updates using React 18's automatic batching or useCallback
      React.startTransition(() => {
        setSelectedUser(user);
        // Set platform based on socialPlatform
        if (user.socialPlatform === "instagram") {
          setPlateform(2);
        } else if (user.socialPlatform === "facebook") {
          setPlateform(1);
        } else if (user.socialPlatform === "flowkar") {
          setPlateform(3);
        } else {
          setPlateform(1); // Default to facebook
        }
        setNewMessage("");
        setIsPolling(false);
        setIsLoadingMore(false); // Reset pagination loading state

        if (!initialLoadMap[user.id]) {
          setLoading(true);
        }
      });

      // Delay fetchMessages to allow backend to process message_read events
      setTimeout(() => {
        if (isMountedRef.current) {
          fetchMessages(true);
        }
      }, 500); // 500ms delay
    }

    if (isMobile) {
      setSidebarVisible(false);
    }
  };

  // Enhanced function to fetch flowkar messages (Dart-style implementation)
  const fetchFlowkarMessages = async (forceRefresh = false, page = 1) => {
    if (!selectedUser || (!forceRefresh && isPolling) || !isMountedRef.current)
      return;

    // Create a fetch timestamp to handle out-of-order responses
    const fetchTimestamp = Date.now();
    const userId = selectedUser.id;

    console.log("🔄 fetchFlowkarMessages called (Dart-style):", {
      userId,
      page,
      forceRefresh,
      isPolling,
      isMounted: isMountedRef.current,
    });

    setIsPolling(true);

    // Set pagination loading state for page > 1 (similar to Dart's isLoadingMore)
    if (page > 1) {
      setIsLoadingMore(true);
      console.log("📊 Setting isLoadingMore to true for page", page);
    }

    try {
      // Use the current abort controller for this request
      if (!initialLoadMap[userId] && page === 1) {
        setLoading(true);
      }

      const signal = abortControllerRef.current?.signal;

      const response = await apiInstance.get(URL.CHAT_MESSAGE_LIST, {
        params: {
          chat_user_id: selectedUser.id,
          page: page,
        },
        headers: {
          brand: BrandId,
          Authorization: `Bearer ${token}`,
        },
        signal, // Add the signal to make this request cancellable
      });

      // Check if component is still mounted and this response is still relevant
      if (
        !isMountedRef.current ||
        !selectedUser ||
        selectedUser.id !== userId
      ) {
        return;
      }

      // Check if this response is stale (an older request finished after a newer one)
      if (
        !lastFetchTimestampRef.current[userId] ||
        fetchTimestamp > lastFetchTimestampRef.current[userId]
      ) {
        // Update last fetch timestamp for this user
        lastFetchTimestampRef.current[userId] = fetchTimestamp;

        // Use correct path for Flowkar API response
        if (
          response.data &&
          response.data.results &&
          response.data.results.status === true
        ) {
          const responseData = response.data.results;
          const flowkarMessages = responseData.data.map((msg) => ({
            id: msg.id,
            created_time: msg.created_at,
            from: {
              username:
                msg.sent_by === selectedUser.id ? selectedUser.name : "You",
              id: msg.sent_by,
            },
            to: {
              data: [
                {
                  username:
                    msg.sent_by === selectedUser.id ? "You" : selectedUser.name,
                  id:
                    msg.sent_by === selectedUser.id
                      ? selectedUser.owner
                      : selectedUser.id,
                },
              ],
            },
            message: msg.message,
            check_message: msg.sent_by === selectedUser.id ? 0 : 1, // 0 for received, 1 for sent
            status: "delivered",
            type: msg.type,
            is_read: msg.is_read,
            post_media_url:
              typeof msg.post_media_url !== "undefined"
                ? msg.post_media_url
                : null,
            files: msg.files || [], // <-- ADD THIS LINE
            title: msg.title || "",
            description: msg.description || "",
            post_user: msg.post_user || null,
          }));

          // Sort messages by timestamp (newest last)
          const sortedMessages = flowkarMessages.sort(
            (a, b) => new Date(a.created_time) - new Date(b.created_time)
          );

          // Update pagination state
          // Check multiple possible pagination indicators
          const totalPages =
            responseData.total_pages || responseData.totalPages || 1;
          const currentPageFromResponse =
            responseData.current_page || responseData.currentPage || page;
          const totalCount = responseData.total || responseData.totalCount || 0;
          const dataLength = responseData.data.length;

          // Hybrid hasMore logic: use totalPages if available, otherwise fallback to page size
          const hasMore =
            typeof totalPages === "number" && page < totalPages
              ? dataLength > 0
              : dataLength === 15;

          setPaginationState((prevState) => ({
            ...prevState,
            [userId]: {
              currentPage: page,
              hasMore: hasMore,
              totalPages: totalPages,
              totalMessages: totalCount,
              lastPageDataLength: dataLength,
            },
          }));

          console.log("📊 Pagination State Updated:", {
            userId,
            page,
            hasMore,
            totalPages,
            totalCount,
            dataLength,
            currentPageFromResponse,
            fullResponseData: responseData,
          });

          // Update messages for this specific user while preserving temporary messages
          setMessagesMap((prevMap) => {
            const prevUserMessages = prevMap[userId] || [];

            // Find temporary messages that haven't been returned by API yet
            const tempMessages = prevUserMessages.filter(
              (msg) =>
                msg.id.toString().startsWith("temp_") &&
                !sortedMessages.some(
                  (apiMsg) =>
                    apiMsg.message === msg.message &&
                    Math.abs(
                      new Date(apiMsg.created_time) - new Date(msg.created_time)
                    ) < 30000
                )
            );

            // For pagination, if page > 1, prepend to existing messages (older messages go to the top)
            // Otherwise, replace with new messages
            let allMessages;
            if (page > 1) {
              const existingMessages = prevUserMessages.filter(
                (msg) => !msg.id.toString().startsWith("temp_")
              );
              allMessages = [
                ...sortedMessages,
                ...existingMessages,
                ...tempMessages,
              ];
            } else {
              allMessages = [...sortedMessages, ...tempMessages];
            }

            // Process pending markAllRead request if any
            processPendingMarkAllRead(userId, allMessages);

            return {
              ...prevMap,
              [userId]: allMessages,
            };
          });

          // Mark initial load as complete for this user
          setInitialLoadMap((prevMap) => ({
            ...prevMap,
            [userId]: true,
          }));

          // Turn off loading indicator once the initial load is complete
          if (page === 1) {
            setLoading(false);
          }

          console.log(
            `✅ Loaded page ${page} for user ${userId}, hasMore: ${
              responseData.data.length > 0 &&
              page < (responseData.total_pages || 1)
            }`
          );
        }
      }
    } catch (error) {
      // Only log errors that aren't from request cancellation
      if (error.name !== "AbortError" && isMountedRef.current) {
        console.error("Error fetching flowkar messages:", error);
      }

      // Turn off loading on error too
      if (isMountedRef.current) {
        setLoading(false);
      }
    } finally {
      if (isMountedRef.current) {
        setIsPolling(false);
        if (page > 1) {
          setIsLoadingMore(false);
        }
      }
    }
  };

  // Enhanced pagination function based on Dart implementation
  const loadMoreMessages = async () => {
    console.log("🔄 loadMoreMessages called (Dart-style pagination)");

    // Check if user is from flowkar platform (equivalent to Dart's platform check)
    if (!selectedUser || selectedUser.socialPlatform !== "flowkar") {
      console.log("❌ Not flowkar user or no selected user");
      return Promise.resolve();
    }

    const userId = selectedUser.id;
    const currentPagination = paginationState[userId];

    console.log("📊 Load More Debug (Dart-style):", {
      userId,
      currentPagination,
      isLoadingMore,
      hasMore: currentPagination?.hasMore,
      currentPage: currentPagination?.page,
    });

    // Dart-style validation: check loading state and hasMore
    // Similar to Dart's: if (!state.isLoadingMore) check
    if (!currentPagination || !currentPagination.hasMore || isLoadingMore) {
      console.log("❌ Cannot load more (Dart-style validation):", {
        noPagination: !currentPagination,
        noMore: !currentPagination?.hasMore,
        alreadyLoading: isLoadingMore,
      });
      return Promise.resolve();
    }

    // Calculate next page (similar to Dart's state.page + 1)
    const nextPage = currentPagination.currentPage + 1;
    console.log(
      `🔄 Loading more messages - page ${nextPage} for user ${userId} (Dart-style)`
    );

    // Set loading state before making request (similar to Dart's state management)
    setIsLoadingMore(true);

    try {
      // Call fetchFlowkarMessages with next page (equivalent to Dart's GetChatMessageListEvent)
      const result = await fetchFlowkarMessages(false, nextPage);
      return result;
    } catch (error) {
      console.error("❌ Error in loadMoreMessages:", error);
      throw error;
    } finally {
      // Always reset loading state (similar to Dart's state management)
      setIsLoadingMore(false);
    }
  };

  // Function to get pagination info for current user
  const getCurrentPaginationInfo = () => {
    if (!selectedUser) return { hasMore: false, isLoading: false };

    const userId = selectedUser.id;
    const pagination = paginationState[userId];

    const info = {
      hasMore: pagination?.hasMore || false,
      isLoading: isLoadingMore,
      currentPage: pagination?.currentPage || 1,
      totalPages: pagination?.totalPages || 1,
    };
    return info;
  };

  const handleChannelChange = (channelName) => {
    // Only cancel requests when changing channels, not on initial load
    if (activeChannel !== channelName) {
      cancelPendingRequests("channel_change");
    }

    setSLoading(true); // Show loader immediately on channel switch
    setSelectedUser(null); // Reset selected user on platform/channel change
    setActiveChannel(channelName);
    fetchUsers(channelName, false); // Not initial load, so don't show loading spinner
  };

  // Mobile detection effect
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    window.addEventListener("resize", checkMobile);
    checkMobile();

    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  // Brand change effect - separate from initial mount
  useEffect(() => {
    if (!isMountedRef.current) return;

    // Create abort controller if it doesn't exist
    if (!abortControllerRef.current) {
      abortControllerRef.current = new AbortController();
    }

    // Reset selected user when brand changes
    setSelectedUser(null);
    fetchUsers(activeChannel, false); // Not initial load, so don't show loading spinner

    return () => {
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current);
      }
    };
  }, [selectedBrand]);

  // Socket event listeners for real-time messaging (matching mobile app)
  useEffect(() => {
    if (!socket) return;

    // Handle received messages (matching mobile app's APIConfig.receivemessage)
    const handleReceiveMessage = (response) => {
      console.log("📨 Received message via socket:", response);

      if (response) {
        const { id, message, type, created_at, sent_by, attachments } =
          response;
        const currentUserId = parseInt(localStorage.getItem("UserId"));
        const senderId = sent_by;

        // If the message is for the currently selected chat, add it as read immediately
        if (
          selectedUser &&
          selectedUser.id.toString() === senderId.toString()
        ) {
          // Create message object matching the API format, mark as read
          const newMessage = {
            id: id,
            message: message,
            type: type,
            created_time: created_at,
            check_message: sent_by === currentUserId ? 1 : 0,
            from: { id: sent_by },
            to: { data: [{ id: selectedUser ? selectedUser.id : senderId }] },
            attachments: attachments || null,
            is_read: true, // Mark as read immediately
            ...(type === "share_post"
              ? {
                  post_media_url: response.post_media_url,
                  post_id: response.post_id,
                  title: response.title,
                  description: response.description,
                  post_user: response.post_user,
                }
              : {}),
          };

          // Add message as read
          const userId = selectedUser.id;
          setMessagesMap((prevMap) => ({
            ...prevMap,
            [userId]: [...(prevMap[userId] || []), newMessage],
          }));

          // Update sidebar latest message for the sender, always move to top
          updateSidebarLatestMessage(senderId, message);

          // No unread indicator for selected user
        } else {
          // Message for another user: add as unread
          const userId = senderId;
          const newMessage = {
            id: id,
            message: message,
            type: type,
            created_time: created_at,
            check_message: sent_by === currentUserId ? 1 : 0,
            from: { id: sent_by },
            to: { data: [{ id: userId }] },
            attachments: attachments || null,
            is_read: false, // Mark as unread
            ...(type === "share_post"
              ? {
                  post_media_url: response.post_media_url,
                  post_id: response.post_id,
                  title: response.title,
                  description: response.description,
                  post_user: response.post_user,
                }
              : {}),
          };
          setMessagesMap((prevMap) => ({
            ...prevMap,
            [userId]: [...(prevMap[userId] || []), newMessage],
          }));
          updateSidebarLatestMessage(senderId, message);
        }
      }
    };

    // Handle sent messages (matching mobile app's APIConfig.sendmessage)
    const handleSendMessage = (response) => {
      console.log("📤 Sent message confirmed via socket:", response);

      if (selectedUser && response) {
        const { id, message, type, created_at, sent_by } = response;

        // Update the temporary message with real data
        const userId = selectedUser.id;
        setMessagesMap((prevMap) => {
          const userMessages = prevMap[userId] || [];
          const updatedMessages = userMessages.map((msg) => {
            // Find temporary message and update it with real data
            if (msg.status === "sending" && msg.message === message) {
              return {
                ...msg,
                id: id,
                created_time: created_at,
                status: "sent",
              };
            }
            return msg;
          });

          return {
            ...prevMap,
            [userId]: updatedMessages,
          };
        });

        // Update sidebar latest message for sent messages too
        updateSidebarLatestMessage(selectedUser.id, message);

        console.log("✅ Sent message updated with server data");
      }
    };

    const handleSocketError = (error) => {
      console.error("Socket error:", error);
    };

    const handleSocketConnect = () => {
      console.log("Socket connected");
    };

    // Handle message read events (when other users read our messages)
    const handleMessageRead = (data) => {
      console.log("📖 Received message_read event:", data);

      if (selectedUser && data) {
        const { message_id, to } = data;
        let updatedMessages = [];

        // Update the message in messagesMap for the selected user
        setMessagesMap((prevMap) => {
          const userId = selectedUser.id;
          const userMessages = prevMap[userId] || [];
          updatedMessages = userMessages.map((msg) =>
            msg.id === message_id ? { ...msg, is_read: true } : msg
          );
          return {
            ...prevMap,
            [userId]: updatedMessages,
          };
        });

        // Recalculate unreadCount using the updated messages
        setUsers((prevUsers) => {
          return prevUsers.map((user) => {
            if (user.id === selectedUser.id) {
              // Count messages that are not read and not sent by the current user
              // Use updatedMessages instead of stale messagesMap
              const unreadCount = updatedMessages.filter(
                (msg) => !msg.is_read && msg.from?.id !== to
              ).length;
              return {
                ...user,
                unreadCount,
                is_read: unreadCount === 0,
              };
            }
            return user;
          });
        });
      }
    };

    // Set up socket listeners (matching mobile app event names)
    socket.on("receive_message", handleReceiveMessage);
    socket.on("send_message", handleSendMessage);
    socket.on("message_read", handleMessageRead);
    socket.on("error", handleSocketError);
    socket.on("connect", handleSocketConnect);

    // Also listen to the old event names for backward compatibility
    socket.on("new_message", (data) => {
      console.log("📨 Legacy new_message event:", data);
      // Fallback to refresh if new format doesn't work
      if (
        selectedUser &&
        (data.from_user_id === selectedUser.id ||
          data.to_user_id === selectedUser.id)
      ) {
        setTimeout(() => fetchMessages(true), 500);
      }
    });

    return () => {
      // Clean up socket listeners
      socket.off("receive_message", handleReceiveMessage);
      socket.off("send_message", handleSendMessage);
      socket.off("message_read", handleMessageRead);
      socket.off("new_message");
      socket.off("error", handleSocketError);
      socket.off("connect", handleSocketConnect);
    };
  }, [socket, selectedUser, activeChannel]);

  // Initial mount effect
  useEffect(() => {
    isMountedRef.current = true;
    abortControllerRef.current = new AbortController();

    // Initial fetch
    fetchUsers(activeChannel, true); // This is initial load, so show loading spinner

    return () => {
      isMountedRef.current = false;
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current);
      }
    };
  }, []); // Empty dependency array for mount only

  // Automatically update unreadCount and is_read for users when messagesMap changes
  // Only update unread counts for users whose messages have actually been loaded
  useEffect(() => {
    setUsers((prevUsers) =>
      prevUsers.map((user) => {
        const userMessages = messagesMap[user.id] || [];

        // Only recalculate unread count if we have actually loaded messages for this user
        // This prevents resetting unread counts to 0 when messages haven't been loaded yet
        if (userMessages.length > 0) {
          const currentUserId = parseInt(localStorage.getItem("UserId"));
          const unreadMessages = userMessages.filter(
            (msg) => !msg.is_read && msg.from?.id !== currentUserId
          );
          const unreadCount = unreadMessages.length;

          // Debug logging for persistent unread indicators
          if (user.unreadCount !== unreadCount) {
            console.log(`📊 Unread count changed for ${user.name}:`, {
              userId: user.id,
              oldCount: user.unreadCount,
              newCount: unreadCount,
              totalMessages: userMessages.length,
              unreadMessages: unreadMessages.map((msg) => ({
                id: msg.id,
                is_read: msg.is_read,
                from: msg.from?.id,
                message: msg.message?.substring(0, 50),
              })),
            });
          }

          return {
            ...user,
            unreadCount,
            is_read: unreadCount === 0,
          };
        }

        // If no messages loaded yet, preserve the original unread count from API
        return user;
      })
    );
  }, [messagesMap]);

  // Note: Removed automatic message marking as read on user selection
  // Messages should only be marked as read through explicit user actions
  // (handled by markAllRead option in handleUserSelect)

  const toggleSidebar = () => {
    setSidebarVisible(!sidebarVisible);
  };

  // Function to update sidebar latest message in real-time
  const updateSidebarLatestMessage = (userId, message) => {
    setUsers((prevUsers) => {
      // Update the user with the latest message and move to top
      const updatedUsers = prevUsers.map((user) =>
        user.id.toString() === userId.toString()
          ? {
              ...user,
              message: message,
              latest_message: message,
              updated_at: new Date().toISOString(),
            }
          : user
      );
      // Find the updated user
      const updatedUser = updatedUsers.find(
        (user) => user.id.toString() === userId.toString()
      );
      // Filter out the updated user from the list
      const otherUsers = updatedUsers.filter(
        (user) => user.id.toString() !== userId.toString()
      );
      // Return the updated user at the top, followed by the rest
      return updatedUser ? [updatedUser, ...otherUsers] : updatedUsers;
    });
    console.log(
      "📝 Updated sidebar latest message for user:",
      userId,
      "Message:",
      message
    );
  };

  const fetchMessages = async (forceRefresh = false) => {
    if (!selectedUser || (!forceRefresh && isPolling) || !isMountedRef.current)
      return;

    // Check if the selected user is from flowkar platform
    if (selectedUser.socialPlatform === "flowkar") {
      await fetchFlowkarMessages(forceRefresh);
      return;
    }

    // Create a fetch timestamp to handle out-of-order responses
    const fetchTimestamp = Date.now();
    const userId = selectedUser.id;

    setIsPolling(true);

    try {
      // Use the current abort controller for this request
      if (!initialLoadMap[userId]) {
        setLoading(true);
      }

      const signal = abortControllerRef.current?.signal;

      const response = await apiInstance.get(URL.SINGLE_CHAT_DETAILS, {
        headers: {
          brand: BrandId,
          conversation: selectedUser.id,
          owner: selectedUser.owner,
          platform: plateform,
        },
        signal, // Add the signal to make this request cancellable
      });
      // Check if component is still mounted and this response is still relevant
      if (
        !isMountedRef.current ||
        !selectedUser ||
        selectedUser.id !== userId
      ) {
        return;
      }

      // Check if this response is stale (an older request finished after a newer one)
      if (
        !lastFetchTimestampRef.current[userId] ||
        fetchTimestamp > lastFetchTimestampRef.current[userId]
      ) {
        // Update last fetch timestamp for this user
        lastFetchTimestampRef.current[userId] = fetchTimestamp;

        // Sort messages by timestamp (newest last)
        const sortedMessages = response.data.data.sort(
          (a, b) => new Date(a.created_time) - new Date(b.created_time)
        );

        // Update messages for this specific user while preserving temporary messages
        setMessagesMap((prevMap) => {
          const prevUserMessages = prevMap[userId] || [];

          // Find temporary messages that haven't been returned by API yet
          const tempMessages = prevUserMessages.filter(
            (msg) =>
              msg.id.startsWith("temp_") &&
              !sortedMessages.some(
                (apiMsg) =>
                  apiMsg.message === msg.message &&
                  Math.abs(
                    new Date(apiMsg.created_time) - new Date(msg.created_time)
                  ) < 30000
              )
          );

          // Update messages for this user only
          let allMessages = [...sortedMessages, ...tempMessages];

          // Process pending markAllRead request if any
          processPendingMarkAllRead(userId, allMessages);

          // Now, just use the API's is_read status
          return {
            ...prevMap,
            [userId]: allMessages,
          };
        });

        // Mark initial load as complete for this user
        setInitialLoadMap((prevMap) => ({
          ...prevMap,
          [userId]: true,
        }));

        // Turn off loading indicator once the initial load is complete
        setLoading(false);
      }
    } catch (error) {
      // Only log errors that aren't from request cancellation
      if (error.name !== "AbortError" && isMountedRef.current) {
        console.error("Error fetching single chat details:", error);
      }

      // Turn off loading on error too
      if (isMountedRef.current) {
        setLoading(false);
      }
    } finally {
      if (isMountedRef.current) {
        setIsPolling(false);
      }
    }
  };

  // Messages and polling effect - separate from other effects
  useEffect(() => {
    // Clear any existing polling interval when user or platform changes
    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current);
    }

    // Reset isPolling state
    setIsPolling(false);

    if (selectedUser?.id && plateform !== null && isMountedRef.current) {
      // Set loading to true only if initial load isn't complete for this user
      if (!initialLoadMap[selectedUser.id]) {
        setLoading(true);
      }

      // Initial fetch for the new user with a small delay
      const timeoutId = setTimeout(() => {
        if (isMountedRef.current && selectedUser?.id) {
          fetchMessages(true);
        }
      }, 150);
      return () => {
        clearTimeout(timeoutId);
        if (pollingIntervalRef.current) {
          clearInterval(pollingIntervalRef.current);
        }
      };
    }
  }, [selectedUser?.id, plateform]); // Only depend on user ID and platform

  // Note: Removed automatic message_read emission on user selection
  // message_read events should only be emitted through explicit user actions
  // (handled by markAllRead option in handleUserSelect)

  const now = new Date();
  const currentTime = now.toISOString();

  const [tempUrls, setTempUrls] = useState(new Set());

  // Cleanup function for temporary URLs
  const cleanupTempUrls = () => {
    tempUrls.forEach((url) => {
      window.URL.revokeObjectURL(url);
    });
    setTempUrls(new Set());
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      cleanupTempUrls();
    };
  }, []);

  const sendFacebookMessage = async () => {
    if (!newMessage.trim() && file.length === 0) return;

    try {
      setMsgloader(true);
      const recipientId = getRecipientId();
      const tempId = `temp_${Date.now()}`;
      const userId = selectedUser.id;

      // Create temporary URLs for preview
      const tempUrls = new Set();
      const filePreview =
        file?.length > 0
          ? file[0].type.startsWith("image/")
            ? (() => {
                const url = window.URL.createObjectURL(file[0]);
                tempUrls.add(url);
                return {
                  attachments: {
                    data: [
                      {
                        image_data: {
                          url: url,
                          preview_url: url,
                        },
                      },
                    ],
                  },
                };
              })()
            : file[0].type.startsWith("video/")
            ? (() => {
                const url = window.URL.createObjectURL(file[0]);
                tempUrls.add(url);
                return {
                  attachments: {
                    data: [
                      {
                        video_data: {
                          url: url,
                          preview_url: url,
                        },
                      },
                    ],
                  },
                };
              })()
            : {}
          : {};

      // Update tempUrls state
      setTempUrls((prev) => new Set([...prev, ...tempUrls]));

      const tempMessage = {
        id: tempId,
        created_time: currentTime,
        from: {
          username: "flexionraj",
          id: selectedUser.owner,
        },
        to: {
          data: [
            {
              username: selectedUser.name,
              id: recipientId,
            },
          ],
        },
        message: newMessage,
        check_message: 1,
        status: "sending",
        ...filePreview,
      };

      // Add temporary message to this user's messages only
      setMessagesMap((prevMap) => ({
        ...prevMap,
        [userId]: [...(prevMap[userId] || []), tempMessage],
      }));

      // Update sidebar latest message immediately for sent messages
      updateSidebarLatestMessage(selectedUser.id, newMessage);

      setNewMessage("");
      setFile([]);

      // Prepare and send form data
      const formData = new FormData();
      formData.append("message", newMessage);
      formData.append("from_user_id", selectedUser.owner);
      formData.append("to_user_id", recipientId);
      formData.append("message_type", file?.length > 0 ? 2 : 1);
      file.forEach((filess) => {
        formData.append("file", filess);
      });

      const response = await apiInstance.post(
        URL.SEND_FACEBOOK_MESSAGE,
        formData,
        {
          headers: { brand: BrandId },
        }
      );

      if (response.status === 200) {
        // Clean up temporary URLs after successful send
        cleanupTempUrls();
        // Wait a moment then force fetch new messages
        setTimeout(() => fetchMessages(true), 500);
      } else {
        throw new Error("Failed to send message");
      }
    } catch (error) {
      console.error("Error sending message:", error);
      // Remove the temporary message from the chat
      const userId = selectedUser.id;
      setMessagesMap((prevMap) => ({
        ...prevMap,
        [userId]: (prevMap[userId] || []).filter(
          (msg) => !msg.id.startsWith("temp_")
        ),
      }));
      // Clean up temporary URLs on error
      cleanupTempUrls();
    } finally {
      setMsgloader(false);
    }
  };

  const sendInstagramMessage = async () => {
    if (!newMessage.trim() && file.length === 0) return;

    try {
      setMsgloader(true);
      const recipientId = getRecipientId();
      const tempId = `temp_${Date.now()}`;
      const userId = selectedUser.id;

      // Create temporary URLs for preview
      const tempUrls = new Set();
      const filePreview =
        file?.length > 0
          ? file[0].type.startsWith("image/")
            ? (() => {
                const url = window.URL.createObjectURL(file[0]);
                tempUrls.add(url);
                return {
                  attachments: {
                    data: [
                      {
                        image_data: {
                          url: url,
                          preview_url: url,
                        },
                      },
                    ],
                  },
                };
              })()
            : file[0].type.startsWith("video/")
            ? (() => {
                const url = window.URL.createObjectURL(file[0]);
                tempUrls.add(url);
                return {
                  attachments: {
                    data: [
                      {
                        video_data: {
                          url: url,
                          preview_url: url,
                        },
                      },
                    ],
                  },
                };
              })()
            : {}
          : {};

      // Update tempUrls state
      setTempUrls((prev) => new Set([...prev, ...tempUrls]));

      const tempMessage = {
        id: tempId,
        created_time: currentTime,
        from: {
          username: "flexionraj",
          id: selectedUser.owner,
        },
        to: {
          data: [
            {
              username: selectedUser.name,
              id: recipientId,
            },
          ],
        },
        message: newMessage,
        check_message: 1,
        status: "sending",
        ...filePreview,
      };

      // Add temporary message to this user's messages only
      setMessagesMap((prevMap) => ({
        ...prevMap,
        [userId]: [...(prevMap[userId] || []), tempMessage],
      }));

      // Update sidebar latest message immediately for sent messages
      updateSidebarLatestMessage(selectedUser.id, newMessage);

      setNewMessage("");
      setFile([]);

      // Prepare and send form data
      const formData = new FormData();
      formData.append("message", newMessage);
      formData.append("from_user_id", selectedUser.owner);
      formData.append("to_user_id", recipientId);
      formData.append("message_type", file?.length > 0 ? 2 : 1);
      file.forEach((filess) => {
        formData.append("file", filess);
      });

      const response = await apiInstance.post(
        URL.SEND_INSTAGRAM_MESSAGE,
        formData,
        {
          headers: { brand: BrandId },
        }
      );

      if (response.status === 200) {
        // Clean up temporary URLs after successful send
        cleanupTempUrls();
        // Wait a moment then force fetch new messages
        setTimeout(() => fetchMessages(true), 500);
      } else {
        throw new Error("Failed to send message");
      }
    } catch (error) {
      console.error("Error sending message:", error);
      // Remove the temporary message from the chat
      const userId = selectedUser.id;
      setMessagesMap((prevMap) => ({
        ...prevMap,
        [userId]: (prevMap[userId] || []).filter(
          (msg) => !msg.id.startsWith("temp_")
        ),
      }));
      // Clean up temporary URLs on error
      cleanupTempUrls();
    } finally {
      setMsgloader(false);
    }
  };

  const sendFlowkarMessage = async () => {
    if (!newMessage.trim() && file.length === 0) return;
    if (!socket || !socket.connected) {
      console.error("Socket not connected");
      alert("Connection error. Please try again.");
      return;
    }

    try {
      setMsgloader(true);
      const currentTime = new Date().toISOString();
      const tempId = `temp_${Date.now()}`;
      const userId = selectedUser.id;

      // Create temporary message for immediate UI feedback
      const tempMessage = {
        id: tempId,
        created_time: currentTime,
        from: {
          username: "You",
          id: localStorage.getItem("UserId") || "current_user",
        },
        to: {
          data: [
            {
              username: selectedUser.name,
              id: selectedUser.id,
            },
          ],
        },
        message: newMessage,
        check_message: 1, // 1 for sent messages
        status: "sending",
        type: file.length > 0 ? "file" : "text",
      };

      // Add temporary message to this user's messages only
      setMessagesMap((prevMap) => ({
        ...prevMap,
        [userId]: [...(prevMap[userId] || []), tempMessage],
      }));

      // Prepare socket data according to the backend specification
      const socketData = {
        Authorization: `Bearer ${token}`,
        message: newMessage,
        type: file.length > 0 ? "text" : "text",
        message_id: tempId,
        file: file.length > 0 ? file[0] : "", // Send first file if multiple
        to: selectedUser.id,
      };

      // Update sidebar latest message immediately for sent messages
      updateSidebarLatestMessage(selectedUser.id, newMessage);

      // Clear input immediately for better UX
      setNewMessage("");
      setFile([]);

      // Emit the message via socket
      socket.emit("send_message", socketData);

      // Message will be confirmed via the "send_message" socket event listener
      // No need for separate response handling - matching mobile app behavior
    } catch (error) {
      console.error("Error sending Flowkar message:", error);
      // Remove the temporary message from the chat
      const userId = selectedUser.id;
      setMessagesMap((prevMap) => ({
        ...prevMap,
        [userId]: (prevMap[userId] || []).filter(
          (msg) => !msg.id.startsWith("temp_")
        ),
      }));
      alert("Failed to send message. Please try again.");
    } finally {
      setMsgloader(false);
    }
  };

  useEffect(() => {
    if (isloading) {
      const timeout = setTimeout(() => {
        setIsLoading(false);
      }, 300);
      return () => clearTimeout(timeout);
    }
  }, [isloading]);

  if (isloading) {
    return <Loader />;
  }

  return (
    <div className="fixed top-[100px] left-0 right-0 bottom-0 flex mb-[30px] pt-4 px-[20px] overflow-hidden">
      {/* Sidebar */}
      <div
        className={`${
          sidebarVisible ? "block" : "hidden"
        } md:block flex-shrink-0 transition-all duration-300 ease-in-out ${
          isMobile ? "absolute z-10 h-full w-[97%]" : ""
        } top-0`}
      >
        <Sidebar
          activeChannel={activeChannel}
          setActiveChannel={handleChannelChange}
          users={users}
          iconlist={iconlist}
          selectedUser={selectedUser}
          onUserSelect={handleUserSelect}
          sloading={sloading}
        />
      </div>

      <div className="flex-grow flex flex-col">
        <ChatModule
          selectedUser={selectedUser}
          toggleSidebar={toggleSidebar}
          isMobile={isMobile}
          sidebarVisible={sidebarVisible}
          messages={getCurrentMessages()} // Get current user's messages only
          loading={loading && !isInitialLoadComplete()} // Only show loading on initial load
          sendFacebookMessage={sendFacebookMessage}
          sendInstagramMessage={sendInstagramMessage}
          sendFlowkarMessage={sendFlowkarMessage}
          newMessage={newMessage}
          setNewMessage={setNewMessage}
          file={file}
          setFile={setFile}
          plateform={plateform}
          msgloader={msgloader}
          loadMoreMessages={loadMoreMessages} // Pagination function
          hasMoreMessages={(() => {
            const paginationInfo = getCurrentPaginationInfo();
            console.log("📊 Props passed to ChatModule:", {
              hasMoreMessages: paginationInfo.hasMore,
              isLoadingMore: paginationInfo.isLoading,
              plateform,
              selectedUser: selectedUser?.id,
              loadMoreMessages: !!loadMoreMessages,
            });
            return paginationInfo.hasMore;
          })()} // Whether more messages are available
          isLoadingMore={getCurrentPaginationInfo().isLoading} // Pagination loading state
        />
      </div>
    </div>
  );
};

export default Index;
