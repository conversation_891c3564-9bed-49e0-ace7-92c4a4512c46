import React, { useRef, useEffect, useState, useContext } from "react";
import sendBtn from "../../../assets/images/svg_icon/sendBtn.svg";
import sendBtn_Dark from "../../../assets/images/svg_icon/sendBtn_Dark.svg";
import Spinner from "../../../helpers/UI/Spinner";
import profile from "../../../assets/images/svg_icon/profile.svg";
import { Dialog, DialogContent, IconButton } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import ZoomInIcon from "@mui/icons-material/ZoomIn";
import ZoomOutIcon from "@mui/icons-material/ZoomOut";
import RestartAltIcon from "@mui/icons-material/RestartAlt";
import DialogTitle from "@mui/material/DialogTitle";
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import Grid from "@mui/material/Grid";
import Chip from "@mui/material/Chip";

import { SocketContext } from "../../../helpers/context/socket";
import { fetchFromStorage } from "../../../helpers/context/storage";
import siteConstant from "../../../helpers/constant/siteConstant";
import { useBrand } from "../../../helpers/context/BrandContext";

const ChatModule = ({
  selectedUser,
  toggleSidebar,
  isMobile,
  messages,
  loading,
  sendFacebookMessage,
  sendInstagramMessage,
  sendFlowkarMessage,
  newMessage,
  setNewMessage,
  file,
  setFile,
  plateform,
  msgloader,
  // Pagination props
  loadMoreMessages,
  hasMoreMessages,
  isLoadingMore,
  currentPage,
}) => {
  const messageEndRef = useRef(null);
  const messagesContainerRef = useRef(null);
  const fileInputRef = useRef(null);
  const [loadedUsers, setLoadedUsers] = useState(new Set());
  const [initialLoading, setInitialLoading] = useState(loading);
  const { selectedBrand } = useBrand();
  const [selectedImage, setSelectedImage] = useState(null);
  const [zoomLevel, setZoomLevel] = useState(1);
  const imageRef = useRef(null);
  const [isDragging, setIsDragging] = useState(false);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const containerRef = useRef(null);
  console.log("--------->", messages);
  // Socket and typing indicator states
  const socket = useContext(SocketContext);
  const [isTyping, setIsTyping] = useState(false);
  const [userTyping, setUserTyping] = useState(false);
  const typingTimeoutRef = useRef(null);
  const token = fetchFromStorage(siteConstant?.INDENTIFIERS?.USERDATA)?.token;
  const myUserId = fetchFromStorage(siteConstant?.INDENTIFIERS?.USERDATA)?.id; // <-- Add this line
  console.log("User Name ========> ", selectedUser);

  // Expose typing control function globally for message handling
  useEffect(() => {
    window.setUserTypingFromSocket = (typingState) => {
      console.log("🔄 Setting typing state from socket:", typingState);
      setUserTyping(typingState);
    };

    // Expose scroll function globally for message handling
    window.scrollToBottomFromSocket = () => {
      console.log("🔄 Scrolling to bottom from socket");
      scrollToBottom("smooth");
    };

    // Cleanup on unmount
    return () => {
      delete window.setUserTypingFromSocket;
      delete window.scrollToBottomFromSocket;
    };
  }, []);

  // Typing indicator functions
  const emitTyping = (isTypingValue) => {
    if (socket && socket.connected && selectedUser) {
      const typingData = {
        Authorization: `Bearer ${token}`,
        to: selectedUser.id,
        from: myUserId,
        is_typing: isTypingValue ? "1" : "0",
      };

      console.log("🔴 Emitting is_typing event:", typingData);

      // Emit with callback to see server response
      socket.emit("is_typing", typingData, (response) => {
        console.log("🟢 Server response to is_typing:", response);
      });
    }
  };

  const handleTypingStart = () => {
    // Only emit typing=true if not already typing
    if (!isTyping) {
      setIsTyping(true);
      emitTyping(true);
      console.log("🔵 Started typing - emitted is_typing: true");
    }

    // Clear existing timeout (this is the debouncing mechanism)
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Set new timeout to stop typing after 1 second of inactivity (matching mobile app)
    typingTimeoutRef.current = setTimeout(() => {
      setIsTyping(false);
      emitTyping(false);
      console.log("🔴 Stopped typing (timeout) - emitted is_typing: false");
    }, 1000); // Changed from 3000ms to 1000ms to match mobile app
  };

  const handleTypingStop = () => {
    // Clear timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Immediately stop typing
    if (isTyping) {
      setIsTyping(false);
      emitTyping(false);
      console.log("🔴 Stopped typing (manual) - emitted is_typing: false");
    }
  };

  // Join socket when user is selected (like mobile app does)
  useEffect(() => {
    if (!socket || !socket.connected || !selectedUser || !token) return;

    const joinSocketData = {
      Authorization: `Bearer ${token}`,
    };

    console.log("🔗 Joining socket for user:", selectedUser.id, joinSocketData);
    socket.emit("join_socket", joinSocketData);

    // Listen for join response
    const handleJoinResponse = (response) => {
      console.log("🔗 Join socket response:", response);
      if (response.message === "JWT Token Required") {
        console.error("JWT Token Required for socket");
      } else if (
        response.success ||
        response.message === "Successfully joined"
      ) {
        console.log("🔗 Successfully joined socket for user:", selectedUser.id);
      }
      // Remove the listener after handling
      socket.off("join_socket", handleJoinResponse);
    };

    // Set up response listener
    socket.on("join_socket", handleJoinResponse);

    // Set a timeout to remove listener if no response
    const timeoutId = setTimeout(() => {
      socket.off("join_socket", handleJoinResponse);
    }, 5000);

    return () => {
      clearTimeout(timeoutId);
      socket.off("join_socket", handleJoinResponse);
    };
  }, [socket, selectedUser, token]);

  // Socket event listeners for typing indicators
  useEffect(() => {
    if (!socket) return;

    // Debug: Listen to ALL socket events to see what's being sent
    const debugAllEvents = (eventName, ...args) => {
      // Log all events for debugging
      console.log("🔍 ALL SOCKET EVENTS:", eventName, args);

      if (eventName.includes("typing") || eventName === "is_typing") {
        console.log(
          "🔍 DEBUG - Received typing-related socket event:",
          eventName,
          "with data:",
          args
        );
      }
    };

    // Add global event listener for debugging
    if (socket.onAny) {
      socket.onAny(debugAllEvents);
    }

    const handleUserTyping = (data) => {
      console.log("🔵 Received typing event:", data);
      console.log("🔵 Event data structure:", JSON.stringify(data, null, 2));
      console.log("🔵 Selected user ID:", selectedUser?.id);

      // Handle different possible data structures from mobile/web
      let isTypingValue = false;
      let fromUserId = null;

      // Check if data has the mobile app format (direct is_typing property)
      if (typeof data === "object" && data.hasOwnProperty("is_typing")) {
        isTypingValue =
          data.is_typing === "1" ||
          data.is_typing === 1 ||
          data.is_typing === true;
        // For mobile app format, the 'from' might be in different property or we need to infer it
        fromUserId =
          data.from ||
          data.user_id ||
          data.userId ||
          data.sender_id ||
          data.senderId;

        // If still no fromUserId found, check if this is a broadcast to the current user
        // In some socket implementations, typing events are sent to the recipient directly
        if (!fromUserId && selectedUser?.id) {
          fromUserId = selectedUser.id;
          console.log(
            "🔵 No sender ID found, assuming typing is from selected user:",
            fromUserId
          );
        }

        console.log(
          "🔵 Mobile format detected - is_typing:",
          data.is_typing,
          "from:",
          fromUserId
        );
      }
      // Check if data has the web format (from and is_typing properties)
      else if (typeof data === "object" && data.hasOwnProperty("from")) {
        fromUserId = data.from;
        isTypingValue =
          data.is_typing === "1" ||
          data.is_typing === 1 ||
          data.is_typing === true;
        console.log(
          "🔵 Web format detected - from:",
          fromUserId,
          "is_typing:",
          data.is_typing
        );
      }
      // Handle direct boolean/string value
      else if (
        typeof data === "boolean" ||
        typeof data === "string" ||
        typeof data === "number"
      ) {
        isTypingValue = data === "1" || data === 1 || data === true;
        // If no from user specified, we might need to assume it's from selected user
        fromUserId = selectedUser?.id;
        console.log(
          "🔵 Direct value format detected - value:",
          data,
          "assuming from:",
          fromUserId
        );
      }

      // Only show typing indicator if it's from the currently selected user
      // Convert both to strings for comparison to handle type mismatches
      const fromUserIdStr = String(fromUserId);
      const selectedUserIdStr = String(selectedUser?.id);

      console.log(
        "🔵 Comparing user IDs - From:",
        fromUserIdStr,
        "Selected:",
        selectedUserIdStr
      );

      // Only show typing indicator if the sender is the selected user
      if (fromUserIdStr === selectedUserIdStr) {
        console.log("🔵 Setting userTyping to:", isTypingValue);
        setUserTyping(isTypingValue);

        // If typing stopped, clear any existing timeout
        if (!isTypingValue && typingTimeoutRef.current) {
          clearTimeout(typingTimeoutRef.current);
        }
      } else {
        console.log(
          "🔵 Ignoring typing event - not from selected user. From:",
          fromUserIdStr,
          "Selected:",
          selectedUserIdStr
        );
      }
    };

    socket.on("is_typing", handleUserTyping);

    socket.on("user_typing", handleUserTyping);
    socket.on("typing", handleUserTyping);
    return () => {
      console.log(
        "🔴 Removing ALL typing listeners for user:",
        selectedUser?.id
      );
      if (socket.offAny) {
        socket.offAny(debugAllEvents);
      }

      socket.off("is_typing", handleUserTyping);
      socket.off("user_typing", handleUserTyping);
      socket.off("typing", handleUserTyping);
    };
  }, [socket, selectedUser]);

  useEffect(() => {
    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
      if (isTyping) {
        emitTyping(false);
      }
    };
  }, [selectedUser]);

  useEffect(() => {
    if (!selectedImage) {
      setZoomLevel(1);
      setPosition({ x: 0, y: 0 });
    }
  }, [selectedImage]);

  const handleZoomIn = () => {
    setZoomLevel((prev) => Math.min(prev + 0.25, 3));
  };

  const handleZoomOut = () => {
    setZoomLevel((prev) => {
      const newZoom = Math.max(prev - 0.25, 0.5);
      if (newZoom === 1) {
        setPosition({ x: 0, y: 0 });
      }
      return newZoom;
    });
  };

  const handleResetZoom = () => {
    setZoomLevel(1);
    setPosition({ x: 0, y: 0 });
  };

  const handleMouseDown = (e) => {
    if (zoomLevel > 1) {
      e.preventDefault();
      setIsDragging(true);
      setDragStart({
        x: e.clientX - position.x,
        y: e.clientY - position.y,
      });
    }
  };

  const handleMouseMove = (e) => {
    if (isDragging && zoomLevel > 1) {
      e.preventDefault();
      const newX = e.clientX - dragStart.x;
      const newY = e.clientY - dragStart.y;

      const container = containerRef.current;
      const image = imageRef.current;

      if (container && image) {
        const containerRect = container.getBoundingClientRect();
        const imageRect = image.getBoundingClientRect();

        const scaledWidth = imageRect.width;
        const scaledHeight = imageRect.height;

        const maxX = Math.max(0, (scaledWidth - containerRect.width) / 2);
        const maxY = Math.max(0, (scaledHeight - containerRect.height) / 2);

        setPosition({
          x: Math.max(-maxX, Math.min(maxX, newX)),
          y: Math.max(-maxY, Math.min(maxY, newY)),
        });
      }
    }
  };

  const handleMouseUp = (e) => {
    if (isDragging) {
      e.preventDefault();
      setIsDragging(false);
    }
  };

  // Add event listeners for mouse up outside the image
  useEffect(() => {
    if (isDragging) {
      window.addEventListener("mouseup", handleMouseUp);
      window.addEventListener("mousemove", handleMouseMove);
      return () => {
        window.removeEventListener("mouseup", handleMouseUp);
        window.removeEventListener("mousemove", handleMouseMove);
      };
    }
  }, [isDragging, dragStart, position, zoomLevel]);

  const hasScrolledRef = useRef(false);
  const lastMessageCountRef = useRef(0);
  const lastSelectedUserRef = useRef(null);
  const hasScrolledForUserRef = useRef(false);

  // Function to scroll to bottom
  const scrollToBottom = (behavior = "smooth") => {
    if (messagesContainerRef.current) {
      const container = messagesContainerRef.current;
      if (behavior === "instant") {
        container.scrollTop = container.scrollHeight;
      } else {
        container.scrollTo({
          top: container.scrollHeight,
          behavior: "smooth",
        });
      }
    }
  };

  // Scroll to bottom when user changes or messages are loaded
  useEffect(() => {
    // Check if user has changed
    const userChanged = lastSelectedUserRef.current !== selectedUser?.id;

    if (userChanged) {
      // Reset scroll state for new user
      hasScrolledRef.current = false;
      hasScrolledForUserRef.current = false;
      lastSelectedUserRef.current = selectedUser?.id;
    }

    // Scroll to bottom when:
    // 1. User changes and messages are loaded
    // 2. New messages are received for current user
    // 3. Initial load is complete
    if (selectedUser && messages && messages.length > 0 && !loading) {
      const messageCountChanged =
        lastMessageCountRef.current !== messages.length;

      if (userChanged) {
        // Use instant scroll for user changes, smooth for new messages
        const scrollBehavior = userChanged ? "instant" : "smooth";

        // Use setTimeout to ensure DOM is updated before scrolling
        setTimeout(() => {
          scrollToBottom(scrollBehavior);
        }, 100);

        hasScrolledRef.current = true;
        hasScrolledForUserRef.current = true;
        lastMessageCountRef.current = messages.length;
      }
    }
  }, [selectedUser, messages, loading]);

  // Additional effect to ensure scrolling on initial load
  useEffect(() => {
    if (
      selectedUser &&
      messages &&
      messages.length > 0 &&
      !loading &&
      messagesContainerRef.current &&
      !hasScrolledForUserRef.current && // Only scroll if we haven't scrolled for this user yet
      !isLoadingMore // Don't scroll during pagination
    ) {
      // Force scroll to bottom after a short delay to ensure DOM is fully rendered
      const timeoutId = setTimeout(() => {
        scrollToBottom("instant");
        hasScrolledForUserRef.current = true;
      }, 100);

      return () => clearTimeout(timeoutId);
    }
  }, [selectedUser?.id, messages?.length, loading, isLoadingMore]);

  // Auto-scroll to bottom when typing indicator appears/disappears
  useEffect(() => {
    if (userTyping && messagesContainerRef.current) {
      // Small delay to ensure typing indicator is rendered in DOM
      setTimeout(() => {
        scrollToBottom("smooth");
        console.log("🔄 Auto-scrolled to bottom for typing indicator");
      }, 100);
    }
  }, [userTyping]);

  // Track which users have been loaded
  useEffect(() => {
    if (selectedUser && loading === false && messages && messages.length > 0) {
      setLoadedUsers((prev) => {
        const newSet = new Set(prev);
        newSet.add(selectedUser.id);
        return newSet;
      });
    }
  }, [selectedUser, loading, messages, selectedBrand]);

  // Set initial loading state when user changes
  useEffect(() => {
    if (selectedUser) {
      // Only show loading for users we haven't loaded before
      setInitialLoading(loading && !loadedUsers.has(selectedUser.id));
    }
  }, [selectedUser, loading, loadedUsers]);

  const handleSendMessage = () => {
    if (newMessage.trim() === "" && file.length === 0) return;

    if (selectedUser.socialPlatform === "facebook") {
      sendFacebookMessage();
    } else if (selectedUser.socialPlatform === "instagram") {
      sendInstagramMessage();
    } else if (selectedUser.socialPlatform === "flowkar") {
      sendFlowkarMessage();
    }

    // Scroll to bottom after sending message (with a small delay to ensure message is added)
    setTimeout(() => {
      scrollToBottom("smooth");
    }, 200);
  };

  const handleKeyDown = (e) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleTypingStop(); // Stop typing when message is sent
      handleSendMessage();
    }
  };

  const handleFileChange = (e) => {
    if (e.target.files && e.target.files.length > 0) {
      setFile(Array.from(e.target.files));
    }
  };

  const handleImageClick = (e, imageUrl) => {
    e.preventDefault();
    e.stopPropagation();
    setSelectedImage(imageUrl);
  };

  const handleCloseDialog = () => {
    setSelectedImage(null);
  };

  // --- Pagination: Scroll to top to load more messages ---
  useEffect(() => {
    const container = messagesContainerRef.current;
    if (!container || !loadMoreMessages) return;
    let isLoading = false;
    let prevFirstMsgId = null;
    let prevFirstMsgOffset = 0;

    const handleScroll = async () => {
      if (
        container.scrollTop <= 20 &&
        hasMoreMessages &&
        !isLoadingMore &&
        typeof loadMoreMessages === "function"
      ) {
        isLoading = true;
        // Find the first message element and its id
        const firstMsgElem = container.querySelector("[data-msg-id]");
        prevFirstMsgId = firstMsgElem
          ? firstMsgElem.getAttribute("data-msg-id")
          : null;
        prevFirstMsgOffset = firstMsgElem ? firstMsgElem.offsetTop : 0;
        const prevScrollHeight = container.scrollHeight;
        await loadMoreMessages();
        setTimeout(() => {
          // After messages update, find the new first message with the same id
          if (prevFirstMsgId) {
            const newFirstMsgElem = container.querySelector(
              `[data-msg-id="${prevFirstMsgId}"]`
            );
            if (newFirstMsgElem) {
              // Adjust scroll so the same message stays at the same position
              const newOffset = newFirstMsgElem.offsetTop;
              container.scrollTop = newOffset - prevFirstMsgOffset;
            } else {
              // fallback: keep scroll at same distance from bottom
              container.scrollTop = container.scrollHeight - prevScrollHeight;
            }
          } else {
            // fallback: keep scroll at same distance from bottom
            container.scrollTop = container.scrollHeight - prevScrollHeight;
          }
          isLoading = false;
        }, 0);
      }
    };
    container.addEventListener("scroll", handleScroll);
    return () => container.removeEventListener("scroll", handleScroll);
  }, [hasMoreMessages, isLoadingMore, loadMoreMessages, messages]);

  // Empty state when no user is selected
  if (!selectedUser || !selectedUser.id) {
    return (
      <div className="flex-grow flex flex-col items-center justify-center bg-[#F9F9F9] rounded-lg ">
        <div className="text-center max-w-lg px-4 font-Ubuntu">
          <h2 className="text-[36px] font-medium text-Red mb-2">
            You don't have a Chat selected.
          </h2>
          <p className="text-Red mb-6 text-[14px]">
            Choose one from your existing Chat, or start a new one.
          </p>
          {/* <button className="bg-Red text-white py-3 px-6 rounded-full font-medium  w-[294px]">
            New Message
          </button> */}
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-[200%] bg-white rounded-lg mx-4 relative overflow-hidden">
      {/* Header */}
      <div className="px-4 py-3 flex items-center sticky top-0 bg-[#FFFFFF] border-b border-[#E0E0E0] pr-4">
        {isMobile && (
          <button onClick={toggleSidebar} className="mr-2">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-6 w-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15 19l-7-7 7-7"
              />
            </svg>
          </button>
        )}
        <div className="flex items-center ">
          <div className="relative">
            <img
              src={selectedUser.avatar || profile}
              alt={selectedUser.name}
              className="h-10 w-10 rounded-full  mt-3"
              onError={(e) => {
                e.target.onerror = null; // Prevent infinite loop
                e.target.src = profile;
                e.target.className = " h-10 w-10 rounded-full border p-2";
              }}
            />
            {selectedUser.status === "active" && (
              <div className="absolute bottom-0 right-0 w-2.5 h-2.5 bg-green-500 rounded-full border-2 border-white"></div>
            )}
          </div>
          <div className="ml-3">
            <h3 className="font-normal text-[#141414]">{selectedUser.name}</h3>
            <p className="font-normal text-[10px] text-[#4F5665]">
              @{selectedUser.user_name}
            </p>
            {/* <p className="text-xs text-gray-500">
              {selectedUser.status === "active" ? "Active now" : "Offline"}
            </p> */}
          </div>
        </div>
      </div>

      {/* Messages - With fixed height and scrolling */}
      <div
        ref={messagesContainerRef}
        className="overflow-auto [&::-webkit-scrollbar]:hidden [-ms-overflow-style:'none'] [scrollbar-width:'none'] flex-grow p-4 bg-white overflow-y-scroll"
        style={{
          height: "calc(100% - 130px)",
          maxHeight: "calc(100vh - 200px)",
          scrollbarGutter: "stable",
        }}
      >
        {/* Pagination Loader at Top - now sticky */}
        {isLoadingMore && (
          <div className="sticky top-0 z-20 bg-white flex items-center justify-center py-2">
            <Spinner />
          </div>
        )}
        {/* Load More Button: Only show if hasMoreMessages and currentPage > 1 */}
        {hasMoreMessages && currentPage > 1 && !isLoadingMore && (
          <div className="flex items-center justify-center py-2">
            <button
              onClick={loadMoreMessages}
              disabled={isLoadingMore}
              className="px-4 py-2 bg-gray-200 rounded hover:bg-gray-300 transition-colors text-sm font-medium"
            >
              {isLoadingMore ? <Spinner size={20} /> : "Load More"}
            </button>
          </div>
        )}
        {loading || initialLoading ? (
          // Show loader when loading is true (API in progress or initial load)
          <div className="flex items-center justify-center h-full">
            <Spinner />
          </div>
        ) : messages && messages.length > 0 ? (
          // Show messages when API succeeded and has messages
          <div className="space-y-3">
            {messages.map((msg, index) => {
              const isOwnMessage = msg.check_message === 1;

              // Show avatar for ALL receiving messages
              const showAvatar = !isOwnMessage;

              // Time grouping logic
              const currentTime = new Date(msg.created_time);
              const prevMsg = messages[index - 1];
              const prevTime = prevMsg ? new Date(prevMsg.created_time) : null;

              const timeDifference = prevTime
                ? (currentTime - prevTime) / (1000 * 60)
                : Infinity; // in minutes

              // Check if the minute has changed for more accurate timestamps
              const currentMinute =
                currentTime.getHours() * 60 + currentTime.getMinutes();
              const prevMinute = prevTime
                ? prevTime.getHours() * 60 + prevTime.getMinutes()
                : -1;
              const minuteChanged = currentMinute !== prevMinute;

              // Check if sender changed
              const senderChanged =
                prevMsg && prevMsg.check_message !== msg.check_message;

              const showTimestamp =
                !prevMsg ||
                timeDifference > 1 || // More than 1 minute passed
                (senderChanged && minuteChanged) || // Sender changed AND minute changed
                (!senderChanged && minuteChanged); // Same sender but minute changed

              return (
                <div key={`${msg.id}-${index}`} data-msg-id={msg.id}>
                  {/* Timestamp separator */}
                  {showTimestamp && (
                    <div className="flex justify-center my-4">
                      <span className="text-xs text-[#563D39] py-1 rounded-full ">
                        {(() => {
                          const today = new Date();
                          const isToday =
                            currentTime.getDate() === today.getDate() &&
                            currentTime.getMonth() === today.getMonth() &&
                            currentTime.getFullYear() === today.getFullYear();
                          return isToday
                            ? `Today, ${currentTime.toLocaleTimeString([], {
                                hour: "2-digit",
                                minute: "2-digit",
                              })}`
                            : currentTime.toLocaleString();
                        })()}
                      </span>
                    </div>
                  )}

                  {/* Message */}
                  <div
                    className={`flex ${
                      isOwnMessage ? "justify-end" : "justify-start"
                    } transition-all duration-300 ease-in-out ${
                      msg.animationState === "entering"
                        ? "transform translate-y-4 opacity-0" // Start position: below and hidden
                        : msg.animationState === "entered"
                        ? "transform translate-y-0 opacity-100" // End position: normal and visible
                        : "transform translate-y-0 opacity-100" // Default for messages without animation
                    }`}
                  >
                    {!isOwnMessage && (
                      <div className="w-10 mr-4 flex-shrink-0">
                        {showAvatar && (
                          <img
                            src={selectedUser.avatar || profile}
                            alt={selectedUser.name}
                            className={
                              selectedUser.avatar
                                ? "w-10 h-10 rounded-full mt-1 object-cover"
                                : "h-10 w-10 rounded-full border p-2"
                            }
                            onError={(e) => {
                              e.target.onerror = null; // Prevent infinite loop
                              e.target.src = profile;
                              e.target.className =
                                " h-10 w-10 rounded-full border p-2";
                            }}
                          />
                        )}
                      </div>
                    )}
                    <div className="flex flex-col items-start">
                      <div
                        className={`max-w-xs md:max-w-md rounded-[12px] px-4 py-2 ${
                          isOwnMessage
                            ? "bg-[#563D39] text-[#FBFBFD] font-Ubuntu font-normal rounded-br-none"
                            : "bg-[#F2F0F0] text-[#141414] font-Ubuntu font-normal rounded-tl-none "
                        }`}
                      >
                        {msg.post_media_url ? (
                          <div className="flex flex-col-reverse items-start">
                            <div
                              className="cursor-pointer mt-2"
                              onClick={(e) =>
                                handleImageClick(
                                  e,
                                  msg.post_media_url.startsWith("http")
                                    ? msg.post_media_url
                                    : `https://api.flowkar.com${msg.post_media_url}`
                                )
                              }
                            >
                              {msg.post_media_url.match(
                                /\.(jpeg|jpg|gif|png|webp|svg)$/i
                              ) ? (
                                <img
                                  src={
                                    msg.post_media_url.startsWith("http")
                                      ? msg.post_media_url
                                      : `https://api.flowkar.com${msg.post_media_url}`
                                  }
                                  alt="Post Media"
                                  className="rounded-md max-w-full hover:opacity-90 transition-opacity"
                                  style={{ maxHeight: "200px" }}
                                />
                              ) : msg.post_media_url.match(
                                  /\.(mp4|webm|ogg|mov)$/i
                                ) ? (
                                <video
                                  src={
                                    msg.post_media_url.startsWith("http")
                                      ? msg.post_media_url
                                      : `https://api.flowkar.com${msg.post_media_url}`
                                  }
                                  controls={false}
                                  className="rounded-md max-w-full hover:opacity-90 transition-opacity"
                                  style={{ maxHeight: "200px" }}
                                />
                              ) : null}
                            </div>
                            {/* Show post_user info below media */}
                            {msg.post_user && (
                              <div className="flex items-center gap-2 mt-2">
                                <img
                                  src={
                                    msg.post_user.profile_image?.startsWith(
                                      "http"
                                    )
                                      ? msg.post_user.profile_image
                                      : `https://api.flowkar.com${msg.post_user.profile_image}`
                                  }
                                  alt={msg.post_user.name}
                                  className="w-8 h-8 rounded-full object-cover border border-white"
                                />
                                <div className="flex flex-col items-start">
                                  <span className="font-medium">
                                    {msg.post_user.name ||
                                      msg.post_user.username}
                                  </span>
                                  <span className="text-[13px] font-normal">
                                    @{msg.post_user.username}
                                  </span>
                                </div>
                              </div>
                            )}
                          </div>
                        ) : (
                          // Otherwise, show message text as usual
                          msg.message && (
                            <p className="break-words">{msg.message}</p>
                          )
                        )}

                        {msg.attachments?.data?.[0]?.image_data && (
                          <div
                            onClick={(e) =>
                              handleImageClick(
                                e,
                                msg.attachments.data[0].image_data.url
                              )
                            }
                            className="cursor-pointer"
                          >
                            <img
                              src={msg.attachments.data[0].image_data.url}
                              alt="Message attachment"
                              className="mt-2 rounded-md max-w-full hover:opacity-90 transition-opacity"
                              style={{ maxHeight: "200px" }}
                            />
                          </div>
                        )}

                        {msg.attachments?.data?.[0]?.video_data && (
                          <div
                            className="mt-2 rounded-md overflow-hidden relative"
                            style={{
                              width: "400px",
                              height: "225px", // 16:9 aspect ratio
                              backgroundColor: "#000",
                            }}
                          >
                            <video
                              src={msg.attachments.data[0].video_data.url}
                              controls
                              className="absolute inset-0 w-full h-full"
                              style={{
                                objectFit: "contain",
                              }}
                              preload="metadata"
                            />
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}

            {/* Typing Indicator - Mobile App Style */}
            <div
              className={`transition-all duration-300 ease-in-out overflow-hidden ${
                userTyping && selectedUser && plateform === 3
                  ? "max-h-24 opacity-100 transform translate-y-0"
                  : "max-h-0 opacity-0 transform translate-y-4"
              }`}
            >
              <div className="flex items-start space-x-3  py-2">
                {/* User Avatar */}
                <div className="flex-shrink-0">
                  <img
                    src={
                      selectedUser?.avatar ||
                      `https://ui-avatars.com/api/?name=${selectedUser?.name}&background=random`
                    }
                    alt={selectedUser?.name}
                    className="w-10 h-10 object-cover rounded-full"
                  />
                </div>

                {/* Typing Content */}
                <div className="flex flex-col space-y-1">
                  {/* Typing Bubble */}
                  <div className="bg-[#F2F0F0] rounded-[12px] rounded-tl-none px-4 py-3 max-w-xs">
                    <div className="flex items-center space-x-2">
                      {/* Animated Dots */}
                      <div className="flex space-x-1">
                        <div
                          className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                          style={{ animationDuration: "1.4s" }}
                        ></div>
                        <div
                          className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                          style={{
                            animationDuration: "1.4s",
                            animationDelay: "0.2s",
                          }}
                        ></div>
                        <div
                          className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                          style={{
                            animationDuration: "1.4s",
                            animationDelay: "0.4s",
                          }}
                        ></div>
                      </div>

                      {/* Typing Text */}
                      <span className="text-xs text-gray-500 italic">
                        typing...
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div ref={messageEndRef} />
          </div>
        ) : messages && messages.length === 0 ? (
          // Show empty state only when API succeeded but returned empty array
          <div className="flex justify-center items-center h-full text-gray-500">
            No messages yet. Start a conversation!
          </div>
        ) : (
          // Show loader for any other case (API failed, messages is null/undefined)
          <div className="flex items-center justify-center h-full">
            <Spinner />
          </div>
        )}
      </div>

      {/* Image Preview Dialog */}
      <Dialog
        open={Boolean(selectedImage)}
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
        className="bg-transparent font-Ubuntu"
        PaperProps={{
          style: {
            maxHeight: "90vh",
            margin: "8px",
            borderRadius: "20px",
            boxShadow: "0 20px 60px rgba(0, 0, 0, 0.1)",
            border: "none",
          },
        }}
      >
        <div className="bg-white rounded-[20px] overflow-y-auto">
          {/* Clean Header */}
          <DialogTitle sx={{ padding: 0 }}>
            <Box
              sx={{
                padding: { xs: 2, sm: 3 },
                borderBottom: "1px solid #f1f5f9",
                backgroundColor: "#ffffff",
              }}
            >
              <Box
                display="flex"
                justifyContent="space-between"
                alignItems="center"
              >
                {/* User Info */}
                {(() => {
                  const msg = messages.find(
                    (m) =>
                      m.post_media_url &&
                      (selectedImage === m.post_media_url ||
                        selectedImage ===
                          `https://api.flowkar.com${m.post_media_url}`)
                  );
                  if (msg && msg.post_user) {
                    const user = msg.post_user;
                    return (
                      <Box display="flex" alignItems="center" gap={2}>
                        <img
                          src={
                            user.profile_image?.startsWith("http")
                              ? user.profile_image
                              : `https://api.flowkar.com${user.profile_image}`
                          }
                          alt={user.name}
                          style={{
                            width: 40,
                            height: 40,
                            borderRadius: "50%",
                            objectFit: "cover",
                            border: "2px solid #fff",
                          }}
                        />
                        <div className="flex flex-col">
                          <Typography
                            fontWeight={600}
                            fontSize={18}
                            color="#000"
                          >
                            {user?.name}
                          </Typography>
                          <Typography fontWeight={500} fontSize={15}>
                            @{user?.username}
                          </Typography>
                        </div>
                      </Box>
                    );
                  }
                  return null;
                })()}
                <IconButton
                  onClick={handleCloseDialog}
                  sx={{
                    color: "#64748b",
                    backgroundColor: "#f8fafc",
                    width: 40,
                    height: 40,
                    "&:hover": {
                      backgroundColor: "#f1f5f9",
                      color: "#475569",
                    },
                    transition: "all 0.2s ease",
                  }}
                >
                  <CloseIcon fontSize="small" />
                </IconButton>
              </Box>
            </Box>
          </DialogTitle>

          {/* Content */}
          <DialogContent
            dividers={false}
            sx={{
              padding: { xs: 2, sm: 3, md: 4 },
              backgroundColor: "#ffffff",
            }}
          >
            {/* Post Preview - Centered media */}
            <Box
              mb={4}
              mt={4}
              display="flex"
              justifyContent="center"
              width="100%"
              px={{ xs: 1, sm: 2 }}
            >
              {(() => {
                const msg = messages.find(
                  (m) =>
                    m.post_media_url &&
                    (selectedImage === m.post_media_url ||
                      selectedImage ===
                        `https://api.flowkar.com${m.post_media_url}`)
                );
                if (!msg) return null;
                const fileUrl = msg.post_media_url.startsWith("http")
                  ? msg.post_media_url
                  : `https://api.flowkar.com${msg.post_media_url}`;
                const isImage = /\.(jpg|jpeg|png|gif|webp|svg)$/i.test(fileUrl);
                const isVideo = /\.(mp4|mov|avi|webm|ogg)$/i.test(fileUrl);
                return isImage ? (
                  <img
                    src={fileUrl}
                    alt="Post media"
                    className="md:w-[340px] w-[250px] md:h-[360px] h-[250px] inline-block rounded-[16px] object-cover mx-auto"
                    style={{
                      boxShadow: "0 8px 25px rgba(0, 0, 0, 0.08)",
                      border: "1px solid #f1f5f9",
                    }}
                  />
                ) : isVideo ? (
                  <video
                    src={fileUrl}
                    controls
                    autoPlay
                    muted
                    className="md:w-[340px] w-[250px] md:h-[360px] h-[250px] inline-block rounded-[16px] object-cover mx-auto"
                    style={{
                      boxShadow: "0 8px 25px rgba(0, 0, 0, 0.08)",
                      border: "1px solid #f1f5f9",
                      backgroundColor: "transparent",
                    }}
                  />
                ) : (
                  <Box
                    sx={{
                      backgroundColor: "#f8fafc",
                      borderRadius: "16px",
                      padding: 3,
                      textAlign: "center",
                      color: "#64748b",
                      width: "100%",
                      maxWidth: "300px",
                      border: "1px solid #e2e8f0",
                    }}
                  >
                    <Typography variant="body2" fontWeight="500">
                      Unsupported media type
                    </Typography>
                  </Box>
                );
              })()}
            </Box>
            {/* Removed title and description section below */}
          </DialogContent>
        </div>
      </Dialog>

      {/* Message Input - Fixed at bottom with z-index */}
      <div className="py-3 bg-white sticky bottom-0 rounded-tl-[24px] rounded-tr-[24px]">
        {file && file.length > 0 && (
          <div className="mb-2 p-2 bg-gray-100 rounded-md flex items-center justify-between">
            <div className="flex items-center">
              <span className="text-sm truncate max-w-xs">{file[0].name}</span>
            </div>
            <button
              onClick={() => setFile([])}
              className="text-gray-500 hover:text-gray-700 "
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>
        )}

        <div className="flex items-center bg-[#FFFFFF] border-t border-[#E0E0E0] pt-[20px] px-3">
          {/* <button
            className="p-2 text-gray-500 hover:text-gray-700 mr-3"
            onClick={() => fileInputRef.current.click()}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-6 w-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"
              />
            </svg>
          </button> */}
          <input
            type="file"
            ref={fileInputRef}
            className="hidden"
            onChange={handleFileChange}
            accept="image/*,video/*"
          />
          <input
            type="text"
            value={newMessage}
            onChange={(e) => {
              setNewMessage(e.target.value);

              // Debounced typing logic (matching mobile app behavior)
              if (e.target.value.length > 0) {
                // User is typing - start/continue typing indicator with debouncing
                handleTypingStart();
              } else {
                // Input is empty - immediately stop typing
                handleTypingStop();
              }
            }}
            onKeyDown={handleKeyDown}
            onBlur={handleTypingStop} // Stop typing when input loses focus
            placeholder="Start a new message"
            className="flex-grow bg-transparent border-none ring-[1px] ring-[#F2F0F0] focus:ring-2 focus:ring-[#563D39] py-3 px-2 rounded-[12px]"
          />

          <button
            onClick={handleSendMessage}
            disabled={
              msgloader || (newMessage.trim() === "" && file.length === 0)
            }
            className={`p-2 ${
              msgloader || (newMessage.trim() === "" && file.length === 0)
                ? "text-gray-400"
                : "text-[#563D39] hover:[#563D39]"
            }`}
          >
            {msgloader ? (
              <div className="flex justify-center items-center h-full">
                <Spinner />
              </div>
            ) : (
              <img
                src={
                  newMessage.trim() !== "" || file.length > 0
                    ? sendBtn_Dark
                    : sendBtn
                }
                alt="Send"
                className="mr-2 ml-4"
              />
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ChatModule;
