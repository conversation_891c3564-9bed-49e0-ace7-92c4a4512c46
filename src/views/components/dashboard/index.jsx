import React, {
  useContext,
  useEffect,
  useState,
  useCallback,
  useMemo,
} from "react";
import { Card, CardContent, Typography, Container, Box } from "@mui/material";
import { styled } from "@mui/styles";
import ChannelConnect from "./ChannelConnect";
import User<PERSON>iewChart from "./userviewchart.jsx";
import Postslider from "../../common/postslider.jsx";
import moment from "moment";
import "./Linechart.css";
import BadGatewayPage from "../../pages/exception/BadGatewayPage";
import SubscriptionPage from "../../common/subscription";
import SurverPopup from "../../common/survey.jsx";
import { CustomTextField } from "../custom/CustomTextField";
import { Formik, Form, Field } from "formik";
import { useNavigate } from "react-router-dom";
import { IntlContext } from "../../../App.jsx";
import recentpostexp from "../../../assets/images/svg_icon/recent.svg";
import Loader from "../../../helpers/UI/Loader.jsx";
import { useBrand } from "../../../helpers/context/BrandContext.jsx";
import dashboardCalendar from "../../../assets/images/svg_icon/dashboardCalendar.svg";
import siteConstant from "../../../helpers/constant/siteConstant.js";
import { setApiMessage } from "../../../helpers/context/toaster.jsx";
import { URL } from "../../../helpers/constant/Url";
import apiInstance from "../../../helpers/Axios/axiosINstance";
import Spinner from "../../../helpers/UI/Spinner.jsx";
import DashboardSkeleton from "../skeletons/DashboardSkeleton.jsx";

// Constants
const PLATFORM_CONFIG = {
  logos: {
    Instagram: siteConstant?.SOCIAL_ICONS.INSTAGRAM_ICON,
    LinkedIn: siteConstant?.SOCIAL_ICONS.LINKEDIN_ICON,
    YouTube: siteConstant?.SOCIAL_ICONS.YOUTUBE_ICON,
    Pinterest: siteConstant?.SOCIAL_ICONS.PINTEREST_ICON,
    Vimeo: siteConstant?.SOCIAL_ICONS.VIMEO_ICON,
    Reddit: siteConstant?.SOCIAL_ICONS.REDDIT_ICON,
    Threads: siteConstant?.SOCIAL_ICONS.THREADS_ICON,
    threads: siteConstant?.SOCIAL_ICONS.THREADS_ICON,
    TikTok: siteConstant?.SOCIAL_ICONS.TIKTOK_ICON,
    x: siteConstant?.SOCIAL_ICONS.TWITTER_ICON,
    Facebook: siteConstant?.SOCIAL_ICONS.FACEBOOK_ICON,
    Tumblr: siteConstant?.SOCIAL_ICONS.TUMBLR_ICON,
    Dailymotion: siteConstant?.SOCIAL_ICONS.DAILYMOTION_ICON,
  },
  colors: {
    LinkedIn: "#0A66C2",
    Instagram: "#C30FB2",
    YouTube: "#FF0302",
    Pinterest: "#E60019",
    Vimeo: "#1EB8EB",
    reddit: "#FC471E",
    threads: "#100E0F",
    tiktok: "#000000",
    Twitter: "#1DA1F2",
    Facebook: "#1877F2",
    tumblr: "#35465C",
    Dailymotion: "#C5D1E3",
    Threads: "#000000",
  },
};

const PLANNER_DATA = [
  { platform: "LinkedIn", time: "10:00 AM Scheduled" },
  { platform: "Pinterest", time: "12:00 PM Scheduled" },
  { platform: "Threads", time: "2:00 PM Scheduled" },
];

const INITIAL_DASHBOARD_CARDS = [
  { title: "Followers", activeno: 0 },
  { title: "Likes", activeno: 0 },
  { title: "Comment", activeno: 0 },
  { title: "Posts", activeno: 0 },
];

const TEXTFIELD_STYLES = {
  "& .MuiOutlinedInput-root": {
    paddingLeft: "",
    "& fieldset": {
      borderRadius: "20px",
      borderColor: "#563D39 !important",
    },
    "&:hover fieldset": {
      borderColor: "#563D39 !important",
    },
    "&.Mui-focused fieldset": {
      borderColor: "#000000 !important",
    },
  },
  "& .MuiOutlinedInput-input": {
    paddingLeft: "10px !important",
    color: "#563D39 !important",
    fontWeight: "normal !important",
    "&:-webkit-autofill": {
      WebkitTextFillColor: "#563D39 !important",
      WebkitBoxShadow: "0 0 0 1000px white inset !important",
      fontWeight: "normal !important",
    },
  },
  "& .MuiInputLabel-root": {
    transform: "translate(45px, 16px) scale(1)",
    color: "#563D39 !important",
    transition: "all 0.2s ease",
    "&.MuiInputLabel-shrink": {
      transform: "translate(45px, -9px) scale(0.75)",
    },
    "&.Mui-focused": {
      color: "#563D39 !important",
    },
  },
};

// Utility functions
const getPlatformBGColor = (platform) => {
  const color = PLATFORM_CONFIG.colors[platform];
  return color ? `${color}1A` : "#E0E0E0";
};

const getDateRange = (timePeriod, range) => {
  if (timePeriod === "custom_range" && range?.fromDate && range?.toDate) {
    return {
      fromDate: moment(range.fromDate).format("YYYY-MM-DD"),
      toDate: moment(range.toDate).format("YYYY-MM-DD"),
    };
  }

  const dateRanges = {
    this_week: {
      fromDate: moment().startOf("week").format("YYYY-MM-DD"),
      toDate: moment().format("YYYY-MM-DD"),
    },
    this_month: {
      fromDate: moment().startOf("month").format("YYYY-MM-DD"),
      toDate: moment().format("YYYY-MM-DD"),
    },
    this_year: {
      fromDate: moment().startOf("year").format("YYYY-MM-DD"),
      toDate: moment().format("YYYY-MM-DD"),
    },
  };

  return dateRanges[timePeriod] || dateRanges.this_week;
};

// Sub-components
const PlannerItem = ({ platform, time }) => {
  const logo = PLATFORM_CONFIG.logos[platform];
  const bgColor = getPlatformBGColor(platform);
  const borderColor = PLATFORM_CONFIG.colors[platform] || "#000000";

  return (
    <div className="flex items-center space-x-3 font-Ubuntu">
      <div
        className="w-[40px] h-[40px] rounded-[10px] flex items-center justify-center border ms-[8px]"
        style={{
          backgroundColor: bgColor,
          borderColor: borderColor,
        }}
      >
        {logo ? (
          <img
            src={logo}
            alt={`${platform} logo`}
            className="w-7 h-7 rounded-full border"
          />
        ) : (
          <span className="text-sm text-gray-600">?</span>
        )}
      </div>
      <div>
        <p className="font-medium text-textRedcolor text-[14px] md:text-[15px]">
          {platform}
        </p>
        <p className="text-[14px] text-[#A9ABAD]">{time}</p>
      </div>
    </div>
  );
};

const WelcomeCard = ({ userData }) => (
  <Box className="flex gap-5 justify-start items-center bg-[#F9F9F9] border-[1px] border-[#E0E0E0] rounded-[12px] w-full px-2 mb-5 h-[120px]">
    <div className="w-[80px] h-[80px] rounded-full overflow-hidden ml-5">
      <img
        src={userData?.profile_image}
        alt="Profile"
        className="w-full h-full object-cover"
      />
    </div>
    <div className="text-left top-card justify-between rounded-[12px] p-5">
      <p className="text-[16px] lg:text-[18px] font-medium py-1 text-[#000000]">
        Welcome Back, {userData?.name}!
      </p>
      <div className="flex justify-start items-center gap-2">
        <img src={dashboardCalendar} alt="Calendar" className="w-5 h-5" />
        <p className="text-[12.5px] lg:text-[16px] font-normal py-2 text-[#000000]">
          Post Scheduled: {userData?.scheduled_count}
        </p>
      </div>
    </div>
  </Box>
);

const AnalyticsSnapshot = ({ dashboardCards }) => {
  const navigate = useNavigate();
  return (
    <Box className="flex flex-col justify-center items-center bg-[#F9F9F9] border-[1px] border-[#E0E0E0] rounded-[12px] w-full px-3 mb-5 h-[180px]">
      <div className="flex justify-between items-center w-full">
        <p className="text-[#563D39] font-medium text-[16px] md:text-[20px] p-3">
          Analytics Snapshot
        </p>
        <p
          onClick={() => navigate("/analytics")}
          className="text-[#563D39] font-medium text-[12px] md:text-[16px] p-3 cursor-pointer"
        >
          See Details
        </p>
      </div>
      <div className="flex gap-5 justify-between items-center w-full">
        {dashboardCards?.map((card, index) => (
          <div
            key={index}
            className="text-center top-card justify-between rounded-[12px] p-5"
          >
            <p className="text-[26px] lg:text-[28px] font-bold py-1 text-[#563D39]">
              {card?.activeno || 0}
            </p>
            <p className="text-[12.5px] lg:text-[14px] font-normal text-[#898989]">
              {card?.title}
            </p>
          </div>
        ))}
      </div>
    </Box>
  );
};

const TodaysPlanner = () => {
  const [plannerData, setPlannerData] = useState([]);
  console.log("plannerData", plannerData);
  const navigate = useNavigate();

  const getPlannerData = async () => {
    try {
      const response = await apiInstance.get(URL.TODAY_SCHEDULED_POST);
      const rawData = response?.data || [];
      const formattedData = rawData.map((item) => {
        const platform = Object.keys(item).find((key) => key !== "uploadtime");

        // Format the time
        const formattedTime = moment(item.uploadtime).format("h:mm A");

        return {
          platform: platform || "Unknown", // Add fallback
          time: `${formattedTime} Scheduled`,
        };
      });

      console.log("Formatted data:", formattedData); // Debug log
      setPlannerData(formattedData);
    } catch (error) {
      console.error("Error fetching planner data:", error);
      setPlannerData([]);
    }
  };

  useEffect(() => {
    getPlannerData();
  }, []);

  return (
    <div className="bg-[#F9F9F9] rounded-xl px-4 border-[1px] border-[#E0E0E0] w-full shadow-sm mb-5 h-[280px] flex flex-col">
      <div className="flex justify-between items-center mb-4">
        <h2 className="font-medium text-[16px] md:text-[20px] p-2 text-[#563D39]">
          Today's Planner
        </h2>
        <button
          className="text-[12px] md:text-[16px] font-medium text-[#563D39] hover:no-underline p-1"
          onClick={() => navigate("/planner")}
        >
          View all
        </button>
      </div>
      <div className="space-y-5 flex-grow overflow-y-auto">
        {plannerData.length > 0 ? (
          plannerData.map((item, index) => (
            <PlannerItem
              key={index}
              platform={item.platform}
              time={item.time}
            />
          ))
        ) : (
          <div className="text-center py-8 flex-grow flex items-center justify-center">
            <div>
              <p className="text-gray-500 text-[16px] font-medium">
                No posts scheduled for today
              </p>
              <p className="text-gray-400 text-[14px] mt-2">
                Schedule your posts to see them here
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

const QuickPostUpload = () => {
  const [description, setDescription] = useState("");
  const brand = localStorage.getItem("BrandId");
  const user = localStorage.getItem("UserId");
  const subscription = localStorage.getItem("subscriptionId");
  const [loadingTextPost, setLoadingTextPost] = useState(false);

  const uploadTextPost = async () => {
    if (!description.trim()) {
      setApiMessage("error", "Description cannot be empty!");
      return;
    }
    const formData = new FormData();
    formData.append("title", "''");
    formData.append("description", description);
    formData.append("tagged_in", JSON.stringify([]));
    formData.append("is_text_post", "True");
    formData.append("is_posted", "True");
    formData.append("files", "[]");
    formData.append("is_video", "false");
    formData.append("thumbnail_files", "[]");
    try {
      setLoadingTextPost(true);
      const response = await apiInstance.post(URL.UPLOAD_POST, formData, {
        headers: {
          brand: brand,
          subscription: subscription,
          user: user,
        },
      });
      setDescription("");
      setApiMessage("success", response?.data?.message);
    } catch (error) {
      setApiMessage("error", error?.message);
    } finally {
      setLoadingTextPost(false);
    }
  };

  return (
    <div className="bg-[#F9F9F9] border border-[#E0E0E0] rounded-[16px] p-4 flex-grow h-full md:h-[240px] flex flex-col">
      <div className="rounded-xl h-full flex flex-col">
        <h2 className="font-medium text-[12px] md:text-[16px] p-1 pb-4 text-[#563D39]">
          Quick Post Upload
        </h2>
        <div className="mb-6 relative flex-grow">
          <Formik
            initialValues={{ postText: "" }}
            onSubmit={(values) => console.log(values)}
          >
            {() => (
              <Form className="h-full">
                <Field
                  as={CustomTextField}
                  name="postText"
                  id="postText"
                  type="text"
                  label="What's on your mind?"
                  placeholder=""
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  fullWidth
                  variant="outlined"
                  multiline
                  rows={4}
                  sx={TEXTFIELD_STYLES}
                />
              </Form>
            )}
          </Formik>
        </div>
        <div className="flex space-x-3 mt-auto">
          <button
            className="bg-[#563D39] text-white font-medium py-2 px-4 rounded-[12px] w-[160px] h-[40px] transition-colors"
            onClick={uploadTextPost}
          >
            {loadingTextPost ? "Uploading..." : "Post"}
          </button>
          {/* <button className="border border-gray-300 text-[#000000] py-2 px-4 w-[160px] h-[40px] bg-[#563D3933] rounded-[12px] transition-colors font-medium">
            Schedule
          </button> */}
        </div>
      </div>
    </div>
  );
};

const RecentPosts = ({ posts, localesData }) => (
  <div className="bg-[#F9F9F9] border border-[#E0E0E0] rounded-[16px] flex-grow md:h-[695px]">
    {posts?.length ? (
      <Postslider posts={posts} sectionTitle="Recent Post" />
    ) : (
      <div className="font-Ubuntu shadow-custom-shadow h-full flex flex-col">
        <p className="text-Red font-bold text-[20px] leading-tight whitespace-nowrap bg-[#F9F9F9] p-3 py-4"></p>
        <div className="text-center flex-grow flex items-center justify-center">
          <div>
            <img src={recentpostexp} alt="No Posts" className="mx-auto mb-4" />
            <div className="pt-3">
              <p className="text-xl font-semibold mb-2">
                {localesData?.USER_WEB?.YOUR_FEED_IS_EMPTY ||
                  "Your feed is empty"}
              </p>
              <p className="text-gray-500 text-[12px]">
                {localesData?.USER_WEB
                  ?.YOU_HAVENT_POSTES_ANYTHING_RECENTLY_SHARE_SOMETHING_NEW ||
                  "You haven't posted anything recently. Share something new!"}
              </p>
            </div>
          </div>
        </div>
      </div>
    )}
  </div>
);

// Main Component
const DashboardPage = () => {
  const intlContext = useContext(IntlContext);
  const { selectedBrand } = useBrand();
  const localesData = IntlContext?.messages;

  // State management
  const [dashboardCards, setDashboardCards] = useState(INITIAL_DASHBOARD_CARDS);
  const [state, setState] = useState({
    graphData: [],
    timePeriod: "this_week",
    range: {},
    posts: [],
    userData: null,
    loading: true,
    error: null,
    showBadGateway: false,
    userinfo: [],
    isOpen: false,
    issurvey: false,
  });

  // Memoized values
  const brandId = useMemo(() => localStorage.getItem("BrandId"), []);

  // API functions
  const fetchUserProfile = useCallback(async () => {
    try {
      const { data } = await apiInstance.get(URL.USER_ADMIN_PROFILE);
      setState((prev) => ({
        ...prev,
        posts: data?.data?.posts || [],
        userData: data?.data,
        loading: false,
      }));
    } catch (error) {
      console.error("Error fetching data:", error);
      setState((prev) => ({
        ...prev,
        error: "Error fetching user data.",
        loading: false,
      }));
    }
  }, []);

  const getUserStatus = useCallback(async () => {
    try {
      const response = await apiInstance.get(URL.USER_STATUS);
      if (response.data) {
        localStorage.setItem("BrandId", response?.data?.brand_id);
      }
    } catch (error) {
      console.error("Error fetching user status:", error);
    }
  }, []);

  const fetchUserHomeData = useCallback(async () => {
    try {
      const response = await apiInstance.get(URL.USERDATA_HOME);
      setState((prev) => ({ ...prev, userinfo: response.data }));

      localStorage.setItem("subscriptionId", response?.data?.subscription_id);
      localStorage.setItem("UserId", response?.data?.user_id);

      if (response?.data?.is_opt_in === false) {
        setTimeout(() => setState((prev) => ({ ...prev, isOpen: true })), 2000);
      }

      if (response?.data?.user_status === "4") {
        setTimeout(
          () => setState((prev) => ({ ...prev, issurvey: true })),
          2000
        );
      }
    } catch (error) {
      console.error("Error fetching user data:", error);
    }
  }, []);

  const fetchDashboardData = useCallback(async () => {
    try {
      const { fromDate, toDate } = getDateRange(state.timePeriod, state.range);
      const response = await apiInstance.get(
        `user-admim-dashboard/?from_date=${fromDate}&to_date=${toDate}`
      );

      const { dislplay_data, graph_data } = response.data;

      if (dislplay_data) {
        setDashboardCards([
          { title: "Followers", activeno: dislplay_data?.followers },
          { title: "Likes", activeno: dislplay_data?.likes },
          { title: "Comment", activeno: dislplay_data?.comments },
          { title: "Posts", activeno: dislplay_data?.posts },
        ]);
      }

      setState((prev) => ({ ...prev, graphData: graph_data }));
    } catch (error) {
      console.error("Error fetching dashboard data:", error);
      setState((prev) => ({
        ...prev,
        error: "Error fetching dashboard data.",
        showBadGateway: true,
      }));
    }
  }, [state.timePeriod, state.range]);

  // Event handlers
  const setTimePeriod = useCallback((period) => {
    setState((prev) => ({ ...prev, timePeriod: period }));
  }, []);

  const setRange = useCallback((range) => {
    setState((prev) => ({ ...prev, range }));
  }, []);

  const setIsOpen = useCallback((isOpen) => {
    setState((prev) => ({ ...prev, isOpen }));
  }, []);

  const setIssurvey = useCallback((issurvey) => {
    setState((prev) => ({ ...prev, issurvey }));
  }, []);

  // Effects
  useEffect(() => {
    getUserStatus();
  }, [getUserStatus]);

  useEffect(() => {
    fetchUserHomeData();
    fetchUserProfile();
  }, [fetchUserHomeData, fetchUserProfile]);

  useEffect(() => {
    fetchDashboardData();
  }, [fetchDashboardData]);

  // Loading and error states
  if (state.showBadGateway) return <BadGatewayPage />;

  if (state.loading) {
    return (
      <DashboardSkeleton />
    );
  }

  if (state.error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <p className="text-[#563D39]">{state.error}</p>
      </div>
    );
  }

  return (
    <div className="px-10 pb-20 font-Ubuntu bg-[#FFFFFF] mt-[26px] mb-[26px]">
      <div className="pt-4">
        {/* Main Content - Two Column Layout with Equal Heights */}
        <div className="flex flex-col md:flex-row gap-5 md:h-[695px]">
          {/* Left Column - WelcomeCard, TodaysPlanner, QuickPostUpload */}
          <div className="w-full md:w-[45%] flex flex-col h-full">
            <WelcomeCard userData={state.userData} />
            <TodaysPlanner />
            <QuickPostUpload />
          </div>

          {/* Right Column - AnalyticsSnapshot, RecentPosts */}
          <div className="w-full md:w-[55%] flex flex-col h-full">
            <AnalyticsSnapshot dashboardCards={dashboardCards} />
            <RecentPosts posts={state.posts} localesData={localesData} />
          </div>
        </div>

        {/* Bottom Section - Chart and Channel Connect */}
        <div className="flex flex-col md:flex-row gap-4 mt-[26px] items-stretch">
          <div className="w-full md:w-[65%]">
            <div className="border border-[#E0E0E0] rounded-[16px] overflow-hidden h-full">
              <UserViewChart
                graphData={state.graphData}
                timePeriod={state.timePeriod}
                onSelect={setTimePeriod}
                setRange={setRange}
              />
            </div>
          </div>
          <div className="w-full md:w-[35%]">
            <div className="h-full">
              <ChannelConnect />
            </div>
          </div>
        </div>
      </div>

      {state.isOpen && (
        <SubscriptionPage isOpen={state.isOpen} setIsOpen={setIsOpen} />
      )}
      {/* {state.issurvey && (
        <SurverPopup issurvey={state.issurvey} setIssurvey={setIssurvey} />
      )} */}
    </div>
  );
};

export default DashboardPage;
